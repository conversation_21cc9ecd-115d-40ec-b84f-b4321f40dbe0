1. T<PERSON><PERSON> đã hoành thành API endpoint ( api-sports-game), tôi muốn tạo FE enduser để hiện thông tin của api-sports-game và hỗ trợ người dùng thao tác. Tôi cần UI tốt SEO cho các bảng tin ( trong tương lai) và SEO tốt cho thông tin league ( hiện tại). Bạn có để xuất nào cho tôi? Bạn có cần rule nào không?

2. TỐt, chúng ta bắt đầu xây dựng dự án nhé. Bắt đầu từ việc setup dự án và tạo cấu trúc mã nguồn nextJS theo rule đã trình bài trong .augment-rules.md

3. Bắt đầu cấu hình .env để xác định port, API soure gốc, domain FE ( dùng cho chạy proxy luôn), sau đó Chúng ta bắt đầu xây dựng trang home và các feature cho trang home trước nhé!

4. <PERSON><PERSON><PERSON> cầu:
<PERSON><PERSON><PERSON> hình để:
npm run dev
npm run start

Đều dùng port trong .env.local
Tôi muốn ứng dụng luôn start theo port đc config trong .env.local , tôi không muốn hard port trong package.json

5. Bắt đầu xây dựng header: Logo, menu ( Home, Fxitures, League, High light video, video), search.

6. Bạn là người có hiểu biết về UI/UX và tuân thủ rule .augment-rules.md trong quá trình lập trình.

7. Theo bạn, trang home nên có phần nào nếu bạn là một chuyên gia về UI/UX

8. Kế hoạch tốt, lập kế hoạch xây dựng chi tiết ( theo mofule/feature/component) để hoàn thiện từng bước 1, Tạo folder LogWorkging lưu tóm tắt các việc đã hoàn thành được đặt tên ( So thứ tự_ ngay thang _nam _ ten featuer/component, được phân loại trong sub folder tương ứng). Read.me trong LogWorking để tóm tắt công việc đã hoàn thành, để tiện follow và research khi cần.

9. Bắt đầu tạo các check list cho phần Hero Section , để chúng ta step by step trên từng feature/component/module