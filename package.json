{"name": "sport-fe-template2", "version": "0.1.0", "private": true, "scripts": {"dev": "dotenv -e .env.local -- sh -c 'next dev -p $PORT'", "build": "next build", "start": "dotenv -e .env.local -- sh -c 'next start -p $PORT'", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"date-fns": "^4.1.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}