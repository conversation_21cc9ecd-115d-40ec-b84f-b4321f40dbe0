import { renderHook, act } from '@testing-library/react-hooks';
import { useMagneticFieldSystem } from '../src/features/home/<USER>/useMagneticFieldSystem';

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 0));
global.cancelAnimationFrame = jest.fn(id => clearTimeout(id));

describe('useMagneticFieldSystem', () => {
  const defaultConfig = {
    maxFields: 10,
    enableVisualization: true,
    fieldLifespan: 5000,
    defaultStrength: 100,
    interactionThreshold: 20,
    enableFieldInteractions: true,
    performanceMode: false,
    accessibilityMode: false
  };

  test('should initialize with empty fields', () => {
    const { result } = renderHook(() => useMagneticFieldSystem(defaultConfig));
    
    expect(result.current.magneticFields).toEqual([]);
    expect(result.current.fieldInteractions).toEqual([]);
    expect(result.current.isActive).toBe(true);
  });

  test('should create a magnetic field', () => {
    const { result } = renderHook(() => useMagneticFieldSystem(defaultConfig));
    
    act(() => {
      result.current.createMagneticField(100, 200);
    });
    
    expect(result.current.magneticFields.length).toBe(1);
    expect(result.current.magneticFields[0].x).toBe(100);
    expect(result.current.magneticFields[0].y).toBe(200);
  });

  test('should respect maxFields limit', () => {
    const { result } = renderHook(() => useMagneticFieldSystem({
      ...defaultConfig,
      maxFields: 3
    }));
    
    act(() => {
      // Create 5 fields
      for (let i = 0; i < 5; i++) {
        result.current.createMagneticField(i * 100, i * 100);
      }
    });
    
    // Should only keep the 3 newest fields
    expect(result.current.magneticFields.length).toBe(3);
  });

  test('should clear all fields', () => {
    const { result } = renderHook(() => useMagneticFieldSystem(defaultConfig));
    
    act(() => {
      result.current.createMagneticField(100, 200);
      result.current.createMagneticField(300, 400);
      result.current.clearAllFields();
    });
    
    expect(result.current.magneticFields).toEqual([]);
    expect(result.current.fieldInteractions).toEqual([]);
  });

  test('should toggle active state', () => {
    const { result } = renderHook(() => useMagneticFieldSystem(defaultConfig));
    
    expect(result.current.isActive).toBe(true);
    
    act(() => {
      result.current.toggleActive();
    });
    
    expect(result.current.isActive).toBe(false);
    
    act(() => {
      result.current.toggleActive();
    });
    
    expect(result.current.isActive).toBe(true);
  });

  test('should provide performance metrics', () => {
    const { result } = renderHook(() => useMagneticFieldSystem(defaultConfig));
    
    const metrics = result.current.getPerformanceMetrics();
    
    expect(metrics).toHaveProperty('fieldCount');
    expect(metrics).toHaveProperty('interactionCount');
    expect(metrics).toHaveProperty('framesPerSecond');
    expect(metrics).toHaveProperty('memoryUsage');
  });
});