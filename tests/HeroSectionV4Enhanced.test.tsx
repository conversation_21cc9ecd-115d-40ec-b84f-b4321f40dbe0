// Hero Section V4 Enhanced Tests
import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { HeroSectionV4Enhanced } from '../src/features/home/<USER>/hero/v4/HeroSectionV4Enhanced';

// Mock the hooks to avoid browser API dependencies in tests
jest.mock('../src/features/home/<USER>/useEnhanced3DParticleSystem', () => ({
    useEnhanced3DParticleSystem: () => ({
        particles: [],
        magneticFields: [],
        emitParticles: jest.fn(),
        triggerGoalCelebration: jest.fn(),
        setMagneticFields: jest.fn(),
        performanceMetrics: { frameRate: 60, renderTime: 0, particleCount: 0 }
    })
}));

jest.mock('../src/features/home/<USER>/useEnhancedAudioSystem', () => ({
    useEnhancedAudioSystem: () => ({
        isInitialized: true,
        loadedAssets: [],
        playingSounds: [],
        playAudio: jest.fn(),
        stopAudio: jest.fn(),
        playUISound: jest.fn(),
        playGoalCelebration: jest.fn(),
        playLiveNotification: jest.fn(),
        setMasterVolume: jest.fn(),
        stopAllAudio: jest.fn()
    })
}));

jest.mock('../src/features/home/<USER>/useMagneticFieldSystem', () => ({
    useMagneticFieldSystem: () => ({
        magneticFields: [],
        fieldInteractions: [],
        createMagneticField: jest.fn(),
        createGoalCelebrationFields: jest.fn(),
        createEnergySurgeFields: jest.fn(),
        clearAllFields: jest.fn(),
        resetSystem: jest.fn(),
        toggleActive: jest.fn(),
        isActive: true,
        visualizationData: { fields: [], interactions: [] },
        getPerformanceMetrics: jest.fn(() => ({
            fieldCount: 0,
            interactionCount: 0,
            framesPerSecond: 60,
            memoryUsage: 0
        })),
        performanceMode: false,
        accessibilityMode: false
    })
}));

// Mock window methods
Object.defineProperty(window, 'requestAnimationFrame', {
    value: (cb: FrameRequestCallback) => setTimeout(cb, 0)
});

Object.defineProperty(window, 'cancelAnimationFrame', {
    value: (id: number) => clearTimeout(id)
});

// Mock navigator vibrate
Object.defineProperty(navigator, 'vibrate', {
    value: jest.fn(),
    writable: true
});

describe('HeroSectionV4Enhanced', () => {
    const mockLiveScores = [
        {
            id: '1',
            homeTeam: { name: 'Team A', flag: '🇺🇸', logo: '' },
            awayTeam: { name: 'Team B', flag: '🇬🇧', logo: '' },
            competition: 'Premier League',
            time: '15:00',
            odds: '1.5',
            status: 'LIVE',
            isHot: true,
            homeScore: 2,
            awayScore: 1
        }
    ];

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('renders without crashing', () => {
        render(<HeroSectionV4Enhanced liveScores={mockLiveScores} />);
        expect(screen.getByRole('main')).toBeInTheDocument();
    });

    test('toggles sound system when sound button is clicked', () => {
        render(<HeroSectionV4Enhanced liveScores={mockLiveScores} />);

        // Find the sound toggle button by its title
        const soundToggleButton = screen.getByTitle('Enable Sound');

        // Click the button
        fireEvent.click(soundToggleButton);

        // The title should change
        expect(screen.getByTitle('Disable Sound')).toBeInTheDocument();

        // Click again to toggle off
        fireEvent.click(screen.getByTitle('Disable Sound'));

        // Should switch back
        expect(screen.getByTitle('Enable Sound')).toBeInTheDocument();
    });

    test('toggles immersive mode when fullscreen button is clicked', () => {
        // Mock document.exitFullscreen and element.requestFullscreen
        document.exitFullscreen = jest.fn().mockResolvedValue(undefined);
        Element.prototype.requestFullscreen = jest.fn().mockResolvedValue(undefined);

        render(<HeroSectionV4Enhanced liveScores={mockLiveScores} />);

        // Find the immersive mode toggle button
        const immersiveButton = screen.getByTitle('Enter Fullscreen');

        // Click the button
        fireEvent.click(immersiveButton);

        // The title should change
        expect(screen.getByTitle('Exit Fullscreen')).toBeInTheDocument();

        // Should have called requestFullscreen
        expect(Element.prototype.requestFullscreen).toHaveBeenCalled();

        // Click again to toggle off
        fireEvent.click(screen.getByTitle('Exit Fullscreen'));

        // Should have called exitFullscreen
        expect(document.exitFullscreen).toHaveBeenCalled();
    });

    test('triggers haptic feedback when button is clicked', () => {
        render(<HeroSectionV4Enhanced liveScores={mockLiveScores} />);

        // Find the haptic feedback button if available
        const hapticButton = screen.queryByTitle('Test Haptic Feedback');

        // If haptic button is present, test it
        if (hapticButton) {
            fireEvent.click(hapticButton);
            expect(navigator.vibrate).toHaveBeenCalled();
        }
    });
});
