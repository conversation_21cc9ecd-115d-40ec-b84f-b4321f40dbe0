import { NewsSection } from "@/features/home/<USER>/NewsSection";
import { LeaguesSection } from "@/features/home/<USER>/LeaguesSection";
import { FixturesSection } from "@/features/home/<USER>/FixturesSection";
import HeroSection from "@/features/home/<USER>/hero";
import { QuickNavSection } from "@/features/home/<USER>/quicknav";
import { mockHeroData } from "@/features/home/<USER>/mockHeroData";
import { mockQuickNavCards } from "@/features/home/<USER>/mockQuickNavData";
import { mockNewsData, mockLeaguesData, mockFixturesData } from "@/features/home/<USER>/mockContentData";

export default async function Home() {
  // TODO: Thay thế bằng gọi service thực tế
  // const { news, leagues, fixtures } = await getHomeData();

  return (
    <div className="min-h-screen">
      {/* Hero Section - Current Version */}
      <HeroSection
        liveScores={mockHeroData.liveScores}
        featuredMatch={mockHeroData.featuredMatch}
        ctaButtons={mockHeroData.ctaButtons}
      />

      {/* Quick Navigation */}
      <QuickNavSection
        cards={mockQuickNavCards}
        title="Quick Access"
      />

      {/* Main Content */}
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="space-y-16">
          {/* News Section */}
          <NewsSection
            news={mockNewsData}
            title="Breaking News"
            maxItems={6}
            showViewAll={true}
          />

          {/* Leagues Section */}
          <LeaguesSection
            leagues={mockLeaguesData}
            title="Popular Leagues"
            maxItems={6}
            showViewAll={true}
          />

          {/* Fixtures Section */}
          <FixturesSection
            fixtures={mockFixturesData}
            title="Latest Fixtures & Results"
            maxItems={6}
            showViewAll={true}
            filterType="all"
          />
        </div>
      </main>
    </div>
  );
}
