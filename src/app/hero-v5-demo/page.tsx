import HeroSectionV5 from '@/features/home/<USER>/hero/v5';

export default function HeroV5DemoPage() {
  return (
    <main>
      <HeroSectionV5
        headline="Đ<PERSON>"
        subheadline="Tr<PERSON><PERSON>hiệ<PERSON> Th<PERSON> Đỉnh <PERSON>"
        description="<PERSON> dõi trự<PERSON> tiếp, cập nhật tức thì, cảm nhận từng khoảnh khắc kịch tính của những trận đấu hấp dẫn nhất."
        variant="immersive"
        colorScheme="dark"
        backgroundType="gradient"
        showLiveScore={true}
        showTrendingMatches={true}
        enableAnimations={true}
        primaryAction={{
          label: "Xem Trực Tiếp",
          href: "/live-matches",
          variant: "solid",
          size: "lg"
        }}
        secondaryAction={{
          label: "Khám Phá Giải Đấu",
          href: "/leagues",
          variant: "outline",
          size: "lg"
        }}
        autoPlay={true}
        reducedMotion={false}
      />
    </main>
  );
}

export const metadata = {
  title: 'Hero Section V5 Demo - Professional UI/UX Design',
  description: 'Demo trang Hero Section V5 với thiết kế UI/UX chuyên nghiệp cho trải nghiệm bóng đá tối ưu',
};
