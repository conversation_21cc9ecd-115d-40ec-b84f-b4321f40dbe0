import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';

interface MagneticFieldDebugPanelProps {
  magneticFieldSystem: ReturnType<typeof import('../../hooks/useMagneticFieldSystem').useMagneticFieldSystem>;
  visible: boolean;
}

export const MagneticFieldDebugPanel: React.FC<MagneticFieldDebugPanelProps> = ({ 
  magneticFieldSystem, 
  visible 
}) => {
  const [metricsHistory, setMetricsHistory] = useState<Array<{
    timestamp: number;
    fps: number;
    fieldCount: number;
    interactionCount: number;
    memoryUsage: number;
  }>>([]);
  
  const [expanded, setExpanded] = useState(false);
  
  // Collect metrics every second
  useEffect(() => {
    if (!visible) return;
    
    const interval = setInterval(() => {
      const metrics = magneticFieldSystem.getPerformanceMetrics();
      setMetricsHistory(prev => {
        const newHistory = [...prev, {
          timestamp: Date.now(),
          fps: metrics.framesPerSecond,
          fieldCount: metrics.fieldCount,
          interactionCount: metrics.interactionCount,
          memoryUsage: metrics.memoryUsage
        }];
        
        // Keep last 60 seconds of data
        if (newHistory.length > 60) {
          return newHistory.slice(newHistory.length - 60);
        }
        return newHistory;
      });
    }, 1000);
    
    return () => clearInterval(interval);
  }, [visible, magneticFieldSystem]);
  
  if (!visible) return null;
  
  return (
    <div className="fixed bottom-0 right-0 bg-black/80 text-white p-2 rounded-tl-md z-50 font-mono text-xs">
      <div className="flex justify-between items-center">
        <h3 className="font-bold">Magnetic Field Debug</h3>
        <button 
          onClick={() => setExpanded(!expanded)}
          className="px-2 py-1 bg-blue-500 rounded"
        >
          {expanded ? 'Collapse' : 'Expand'}
        </button>
      </div>
      
      <div className="grid grid-cols-2 gap-2 mt-2">
        <div>Fields: {magneticFieldSystem.magneticFields.length}</div>
        <div>Interactions: {magneticFieldSystem.fieldInteractions.length}</div>
        <div>FPS: {magneticFieldSystem.getPerformanceMetrics().framesPerSecond}</div>
        <div>Memory: {(magneticFieldSystem.getPerformanceMetrics().memoryUsage / 1024).toFixed(1)} KB</div>
      </div>
      
      {expanded && (
        <div className="mt-4 w-80 h-40">
          <Line
            data={{
              labels: metricsHistory.map((_, i) => i.toString()),
              datasets: [
                {
                  label: 'FPS',
                  data: metricsHistory.map(m => m.fps),
                  borderColor: 'rgb(75, 192, 192)',
                  tension: 0.1
                },
                {
                  label: 'Fields',
                  data: metricsHistory.map(m => m.fieldCount),
                  borderColor: 'rgb(255, 99, 132)',
                  tension: 0.1
                }
              ]
            }}
            options={{
              scales: {
                y: {
                  beginAtZero: true
                }
              },
              animation: false,
              responsive: true,
              maintainAspectRatio: false
            }}
          />
        </div>
      )}
      
      <div className="mt-2 flex gap-2">
        <button 
          onClick={() => magneticFieldSystem.clearAllFields()}
          className="px-2 py-1 bg-red-500 rounded"
        >
          Clear Fields
        </button>
        <button 
          onClick={() => magneticFieldSystem.toggleActive()}
          className={`px-2 py-1 rounded ${magneticFieldSystem.isActive ? 'bg-yellow-500' : 'bg-green-500'}`}
        >
          {magneticFieldSystem.isActive ? 'Pause' : 'Resume'}
        </button>
      </div>
    </div>
  );
};