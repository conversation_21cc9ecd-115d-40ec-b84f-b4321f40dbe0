'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { HeroV5Props, LiveFixture } from './types';
import { useRealTimeFixtures } from './hooks/useRealTimeFixtures';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

const HeroSectionV5New: React.FC<HeroV5Props> = ({
  title = "Trải Nghiệm Bóng Đá Đỉnh Cao",
  subtitle = "Theo dõi trực tiếp, cảm nhận từng khoảnh khắc kịch tính",
  featuredMatch,
  liveMatches = [],
  enableLiveUpdates = true,
  showMatchHighlights = true,
  theme = 'auto',
  backgroundVideo,
  heroImage,
  primaryCTA = {
    label: "Xem Trực Tiếp",
    href: "/live",
    variant: "primary"
  },
  secondaryCTA = {
    label: "Khám Phá Thêm",
    href: "/matches",
    variant: "secondary"
  }
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedMatch, setSelectedMatch] = useState<LiveFixture | null>(featuredMatch || null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);

  // Suppress unused variable warnings for future features
  void liveMatches;
  void showMatchHighlights;
  void theme;
  void isVideoLoaded;

  // Real-time updates for live matches
  const { fixtures: liveFixtures } = useRealTimeFixtures(
    { status: 'LIVE' },
    { autoRefresh: enableLiveUpdates, refreshInterval: 5000 }
  );

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  // Auto-select featured match or first live match
  useEffect(() => {
    if (!selectedMatch) {
      const firstLive = liveFixtures.find(f => f.status === 'LIVE') || liveFixtures[0];
      if (firstLive) {
        setSelectedMatch(firstLive);
      }
    }
  }, [liveFixtures, selectedMatch]);

  const handleMatchSelect = useCallback((match: LiveFixture) => {
    setSelectedMatch(match);
  }, []);

  const getMatchStatusDisplay = (match: LiveFixture) => {
    switch (match.status) {
      case 'LIVE':
        return (
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-red-500 font-bold">TRỰC TIẾP</span>
          </div>
        );
      case 'HT':
        return <span className="text-orange-500 font-semibold">HIỆP 1</span>;
      case 'FT':
        return <span className="text-gray-500">KẾT THÚC</span>;
      default:
        return <span className="text-blue-500">{format(new Date(match.date), 'HH:mm', { locale: vi })}</span>;
    }
  };

  const liveMatchesCount = liveFixtures.filter(f => f.status === 'LIVE').length;

  return (
    <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Background Video/Image */}
      {backgroundVideo && (
        <div className="absolute inset-0 z-0">
          <video
            autoPlay
            muted
            loop
            playsInline
            className="w-full h-full object-cover opacity-30"
            onLoadedData={() => setIsVideoLoaded(true)}
          >
            <source src={backgroundVideo} type="video/mp4" />
          </video>
        </div>
      )}

      {heroImage && !backgroundVideo && (
        <div className="absolute inset-0 z-0">
          <Image
            src={heroImage}
            alt="Hero background"
            fill
            className="object-cover opacity-30"
            priority
          />
        </div>
      )}

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/40 z-10"></div>

      {/* Content */}
      <div className="relative z-20 container mx-auto px-4 py-8 min-h-screen flex flex-col">
        {/* Header */}
        <div className="text-center mb-8 pt-16">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-4 leading-tight">
            {title}
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
            {subtitle}
          </p>

          {/* Live Indicator */}
          {liveMatchesCount > 0 && (
            <div className="inline-flex items-center space-x-3 bg-red-500/20 backdrop-blur-sm rounded-full px-6 py-3 border border-red-500/30">
              <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-white font-semibold">
                {liveMatchesCount} trận đang diễn ra
              </span>
              <span className="text-gray-300 text-sm">
                {format(currentTime, 'HH:mm:ss', { locale: vi })}
              </span>
            </div>
          )}
        </div>

        {/* Featured Match */}
        {selectedMatch && (
          <div className="flex-1 flex items-center justify-center mb-8">
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 max-w-4xl w-full">
              {/* Match Header */}
              <div className="text-center mb-6">
                <div className="text-white/80 text-sm mb-2">{selectedMatch.league.name}</div>
                {getMatchStatusDisplay(selectedMatch)}
              </div>

              {/* Teams */}
              <div className="grid grid-cols-3 items-center gap-8 mb-6">
                {/* Home Team */}
                <div className="text-center">
                  <div className="relative w-20 h-20 mx-auto mb-4">
                    {selectedMatch.homeTeam.logo ? (
                      <Image
                        src={selectedMatch.homeTeam.logo}
                        alt={selectedMatch.homeTeam.name}
                        fill
                        className="object-contain"
                      />
                    ) : (
                      <div className="w-full h-full bg-white/20 rounded-full flex items-center justify-center text-white font-bold text-xl">
                        {selectedMatch.homeTeam.name.substring(0, 2)}
                      </div>
                    )}
                  </div>
                  <h3 className="text-white font-bold text-lg">{selectedMatch.homeTeam.name}</h3>
                </div>

                {/* Score */}
                <div className="text-center">
                  <div className="text-6xl font-bold text-white mb-2">
                    {selectedMatch.score.home ?? '-'} : {selectedMatch.score.away ?? '-'}
                  </div>
                  {selectedMatch.status === 'LIVE' && (
                    <div className="text-green-400 text-sm animate-pulse">
                      Đang cập nhật...
                    </div>
                  )}
                </div>

                {/* Away Team */}
                <div className="text-center">
                  <div className="relative w-20 h-20 mx-auto mb-4">
                    {selectedMatch.awayTeam.logo ? (
                      <Image
                        src={selectedMatch.awayTeam.logo}
                        alt={selectedMatch.awayTeam.name}
                        fill
                        className="object-contain"
                      />
                    ) : (
                      <div className="w-full h-full bg-white/20 rounded-full flex items-center justify-center text-white font-bold text-xl">
                        {selectedMatch.awayTeam.name.substring(0, 2)}
                      </div>
                    )}
                  </div>
                  <h3 className="text-white font-bold text-lg">{selectedMatch.awayTeam.name}</h3>
                </div>
              </div>

              {/* Match Info */}
              {selectedMatch.venue && (
                <div className="text-center text-white/70 text-sm">
                  📍 {selectedMatch.venue.name}, {selectedMatch.venue.city}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Live Matches Carousel */}
        {liveFixtures.length > 0 && (
          <div className="mb-8">
            <h3 className="text-white text-xl font-semibold mb-4 text-center">
              Các trận đang diễn ra
            </h3>
            <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-hide">
              {liveFixtures.slice(0, 5).map((match) => (
                <div
                  key={match.id}
                  onClick={() => handleMatchSelect(match)}
                  className={`flex-shrink-0 bg-white/10 backdrop-blur-sm rounded-xl p-4 border cursor-pointer transition-all ${selectedMatch?.id === match.id
                    ? 'border-blue-400 bg-blue-500/20'
                    : 'border-white/20 hover:border-white/40'
                    }`}
                >
                  <div className="flex items-center space-x-3 min-w-[200px]">
                    <div className="text-white text-sm">
                      {match.homeTeam.name} vs {match.awayTeam.name}
                    </div>
                    <div className="text-white font-bold">
                      {match.score.home ?? 0} - {match.score.away ?? 0}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Call to Actions */}
        <div className="text-center space-y-4 md:space-y-0 md:space-x-6 md:flex md:justify-center">
          <button className="w-full md:w-auto bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-full transition-colors text-lg">
            {primaryCTA.label}
          </button>
          <button className="w-full md:w-auto bg-white/20 hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-full border border-white/30 transition-colors text-lg backdrop-blur-sm">
            {secondaryCTA.label}
          </button>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float"></div>
      <div className="absolute bottom-20 right-10 w-16 h-16 bg-blue-500/20 rounded-full animate-float-delayed"></div>
      <div className="absolute top-1/2 right-20 w-12 h-12 bg-green-500/20 rounded-full animate-float"></div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-15px); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
        .animate-float-delayed {
          animation: float-delayed 8s ease-in-out infinite;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </section>
  );
};

export default HeroSectionV5New;
