'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { HeroV5Props, LiveFixture } from './types';
import { useRealTimeFixtures } from './hooks/useRealTimeFixtures';
import {
  Typo<PERSON>,
  Button,
  Card,
  StatusIndicator,
  TeamLogo,
  MatchScore,
  Container,
  LoadingSkeleton
} from './components/DesignSystem';

// Featured Match Component
const FeaturedMatch: React.FC<{
  match: LiveFixture;
  onClick?: () => void;
}> = ({ match, onClick }) => (
  <Card variant="glass" padding="xl" onClick={onClick} className="max-w-4xl mx-auto">
    {/* League Header */}
    <div className="text-center mb-8">
      <Typography.Caption className="text-white/60 mb-3">
        {match.league.name}
      </Typography.Caption>
      <StatusIndicator
        status={match.status === 'LIVE' ? 'live' : match.status === 'FT' ? 'finished' : 'upcoming'}
        showPulse={match.status === 'LIVE'}
        size="lg"
      />
    </div>

    {/* Teams & Score */}
    <div className="grid grid-cols-3 items-center gap-8 mb-6">
      {/* Home Team */}
      <div className="text-center">
        <TeamLogo team={match.homeTeam} size="xl" />
        <Typography.H2 className="text-white mt-4">
          {match.homeTeam.name}
        </Typography.H2>
      </div>

      {/* Score */}
      <MatchScore
        homeScore={match.score.home}
        awayScore={match.score.away}
        status={match.status}
        size="xl"
      />

      {/* Away Team */}
      <div className="text-center">
        <TeamLogo team={match.awayTeam} size="xl" />
        <Typography.H2 className="text-white mt-4">
          {match.awayTeam.name}
        </Typography.H2>
      </div>
    </div>

    {/* Match Info */}
    {match.venue && (
      <div className="text-center">
        <Typography.Caption className="text-white/70">
          📍 {match.venue.name}, {match.venue.city}
        </Typography.Caption>
      </div>
    )}
  </Card>
);

// Trending Matches Component
const TrendingMatches: React.FC<{
  matches: LiveFixture[];
  onMatchSelect: (match: LiveFixture) => void;
}> = ({ matches, onMatchSelect }) => (
  <div className="space-y-6">
    <Typography.H2 className="text-white text-center">
      Trận đấu nổi bật
    </Typography.H2>
    <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-hide justify-center">
      {matches.slice(0, 4).map((match) => (
        <Card
          key={match.id}
          variant="glass"
          padding="md"
          onClick={() => onMatchSelect(match)}
          className="min-w-[300px] flex-shrink-0"
        >
          <div className="flex items-center justify-between mb-3">
            <Typography.Caption className="text-white/60">
              {match.league.name}
            </Typography.Caption>
            {match.status === 'LIVE' && (
              <StatusIndicator status="live" size="sm" />
            )}
          </div>

          <div className="grid grid-cols-3 items-center gap-4">
            <div className="text-center">
              <TeamLogo team={match.homeTeam} size="sm" />
              <Typography.Caption className="text-white mt-2 block">
                {match.homeTeam.name}
              </Typography.Caption>
            </div>

            <MatchScore
              homeScore={match.score.home}
              awayScore={match.score.away}
              status={match.status}
              size="sm"
              showStatus={false}
            />

            <div className="text-center">
              <TeamLogo team={match.awayTeam} size="sm" />
              <Typography.Caption className="text-white mt-2 block">
                {match.awayTeam.name}
              </Typography.Caption>
            </div>
          </div>
        </Card>
      ))}
    </div>
  </div>
);

// Loading State Component
const LoadingState: React.FC = () => (
  <section className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center">
    <Container size="md" className="text-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-400 mx-auto mb-6"></div>
      <Typography.H2 className="text-white mb-4">
        Đang tải trải nghiệm bóng đá...
      </Typography.H2>
      <div className="space-y-4">
        <LoadingSkeleton className="h-8 w-3/4 mx-auto" />
        <LoadingSkeleton className="h-6 w-1/2 mx-auto" />
      </div>
    </Container>
  </section>
);

// Main Hero Component
const HeroSectionV5Expert: React.FC<HeroV5Props> = ({
  headline = "Đam Mê Bóng Đá",
  subheadline = "Trải Nghiệm Thể Thao Đỉnh Cao",
  description = "Theo dõi trực tiếp, cập nhật tức thì, cảm nhận từng khoảnh khắc kịch tính của những trận đấu hấp dẫn nhất.",
  variant = 'immersive',
  backgroundType = 'gradient',
  backgroundMedia,
  showLiveScore = true,
  showTrendingMatches = true,
  enableAnimations = true,
  primaryAction = {
    label: "Xem Trực Tiếp",
    href: "/live",
    variant: "solid",
    size: "lg"
  },
  secondaryAction = {
    label: "Khám Phá",
    href: "/explore",
    variant: "outline",
    size: "lg"
  },
  autoPlay = true,
  reducedMotion = false
}) => {
  const [selectedMatch, setSelectedMatch] = useState<LiveFixture | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Suppress unused variable warnings for future features
  void variant;

  // Data fetching with better error handling
  const {
    fixtures: liveMatches,
    loading: liveLoading,
    error: liveError
  } = useRealTimeFixtures(
    { status: 'LIVE' },
    { autoRefresh: autoPlay, refreshInterval: 5000 }
  );

  const {
    fixtures: trendingMatches,
    loading: trendingLoading
  } = useRealTimeFixtures(
    { status: 'LIVE,UPCOMING' },
    { autoRefresh: autoPlay, refreshInterval: 10000 }
  );

  // Featured match selection logic
  const featuredMatch = useMemo(() => {
    return selectedMatch ||
      liveMatches.find(m => m.status === 'LIVE' && m.isHot) ||
      liveMatches.find(m => m.status === 'LIVE') ||
      trendingMatches.find(m => m.isHot) ||
      trendingMatches[0];
  }, [selectedMatch, liveMatches, trendingMatches]);

  // Loading state management
  useEffect(() => {
    if (!liveLoading && !trendingLoading) {
      const timer = setTimeout(() => setIsLoading(false), 500);
      return () => clearTimeout(timer);
    }
  }, [liveLoading, trendingLoading]);

  const handleMatchSelect = useCallback((match: LiveFixture) => {
    setSelectedMatch(match);
  }, []);

  const handlePrimaryAction = useCallback(() => {
    window.location.href = primaryAction.href;
  }, [primaryAction.href]);

  const handleSecondaryAction = useCallback(() => {
    window.location.href = secondaryAction.href;
  }, [secondaryAction.href]);

  // Background styles
  const backgroundStyles = useMemo(() => {
    switch (backgroundType) {
      case 'gradient':
        return 'bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900';
      case 'image':
        return backgroundMedia ? 'bg-black' : 'bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900';
      case 'video':
        return 'bg-black';
      default:
        return 'bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900';
    }
  }, [backgroundType, backgroundMedia]);

  // Show loading state
  if (isLoading) {
    return <LoadingState />;
  }

  const liveCount = liveMatches.length;

  return (
    <section className={`relative min-h-screen overflow-hidden ${backgroundStyles}`}>
      {/* Background Media */}
      {backgroundType === 'image' && backgroundMedia && (
        <div className="absolute inset-0 z-0">
          <Image
            src={backgroundMedia}
            alt="Hero background"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>
      )}

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/40 z-10"></div>

      {/* Content */}
      <div className="relative z-20 min-h-screen flex flex-col justify-center py-16">
        <Container size="xl">
          {/* Header Section */}
          <div className="text-center mb-16">
            <Typography.H1 className="text-white mb-6">
              {headline}
            </Typography.H1>
            <Typography.H2 className="text-blue-200 mb-8">
              {subheadline}
            </Typography.H2>
            <Typography.Body className="text-white/80 mb-12 max-w-3xl mx-auto">
              {description}
            </Typography.Body>

            {/* Live Status */}
            {showLiveScore && liveCount > 0 && (
              <div className="flex justify-center mb-12">
                <StatusIndicator
                  status="live"
                  count={liveCount}
                  size="lg"
                />
              </div>
            )}
          </div>

          {/* Featured Match */}
          {showLiveScore && featuredMatch && (
            <div className="mb-16">
              <FeaturedMatch
                match={featuredMatch}
                onClick={() => handleMatchSelect(featuredMatch)}
              />
            </div>
          )}

          {/* Trending Matches */}
          {showTrendingMatches && trendingMatches.length > 0 && (
            <div className="mb-16">
              <TrendingMatches
                matches={trendingMatches}
                onMatchSelect={handleMatchSelect}
              />
            </div>
          )}

          {/* Call to Actions */}
          <div className="text-center space-y-6 md:space-y-0 md:space-x-8 md:flex md:justify-center">
            <Button
              variant="primary"
              size="xl"
              onClick={handlePrimaryAction}
              icon={<span>🔴</span>}
            >
              {primaryAction.label}
            </Button>
            <Button
              variant="secondary"
              size="xl"
              onClick={handleSecondaryAction}
              icon={<span>⚡</span>}
            >
              {secondaryAction.label}
            </Button>
          </div>

          {/* Error State */}
          {liveError && (
            <div className="text-center mt-8">
              <Typography.Caption className="text-red-400">
                Không thể tải dữ liệu trực tiếp. Vui lòng thử lại sau.
              </Typography.Caption>
            </div>
          )}
        </Container>
      </div>

      {/* Decorative Elements */}
      {enableAnimations && !reducedMotion && (
        <>
          <div className="absolute top-20 left-10 w-20 h-20 bg-blue-500/10 rounded-full animate-float"></div>
          <div className="absolute bottom-20 right-10 w-16 h-16 bg-green-500/10 rounded-full animate-float-delayed"></div>
          <div className="absolute top-1/2 right-20 w-12 h-12 bg-purple-500/10 rounded-full animate-float"></div>
        </>
      )}

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-15px); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
        .animate-float-delayed {
          animation: float-delayed 8s ease-in-out infinite;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </section>
  );
};

export default HeroSectionV5Expert;
