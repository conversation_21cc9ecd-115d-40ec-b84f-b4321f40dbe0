'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { LiveFixture } from '../types';
import { formatDistanceToNow, format } from 'date-fns';
import { vi } from 'date-fns/locale';

// Import BroadcastLink type for future use
// import { BroadcastLink } from '../types';

interface LiveMatchCardProps {
  fixture: LiveFixture;
  onClick?: (fixture: LiveFixture) => void;
  showBroadcastLinks?: boolean;
  compact?: boolean;
  className?: string;
}

const LiveMatchCard: React.FC<LiveMatchCardProps> = ({
  fixture,
  onClick,
  showBroadcastLinks = false,
  compact = false,
  className = ''
}) => {
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

  // Future implementation for broadcast links
  // const [broadcastLinks, setBroadcastLinks] = useState<BroadcastLink[]>([]);
  // const [loadingLinks, setLoadingLinks] = useState(false);

  // Suppress unused variable warnings for future features
  void showBroadcastLinks;

  const handleImageError = useCallback((imageKey: string) => {
    setImageErrors(prev => ({ ...prev, [imageKey]: true }));
  }, []);

  const handleCardClick = useCallback(() => {
    if (onClick) {
      onClick(fixture);
    }
  }, [onClick, fixture]);

  const getStatusDisplay = () => {
    switch (fixture.status) {
      case 'LIVE':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 animate-pulse">
            <span className="w-2 h-2 bg-red-500 rounded-full mr-1 animate-ping"></span>
            LIVE
          </span>
        );
      case 'HT':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
            HT
          </span>
        );
      case 'FT':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            FT
          </span>
        );
      case 'UPCOMING':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {format(new Date(fixture.date), 'HH:mm', { locale: vi })}
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {fixture.status}
          </span>
        );
    }
  };

  const getTimeDisplay = () => {
    const matchDate = new Date(fixture.date);
    const now = new Date();

    if (fixture.status === 'LIVE' || fixture.status === 'HT') {
      return 'Đang diễn ra';
    }

    if (fixture.status === 'FT') {
      return `Kết thúc ${formatDistanceToNow(matchDate, { addSuffix: true, locale: vi })}`;
    }

    if (matchDate > now) {
      return `${formatDistanceToNow(matchDate, { addSuffix: true, locale: vi })}`;
    }

    return format(matchDate, 'dd/MM/yyyy HH:mm', { locale: vi });
  };

  const TeamLogo: React.FC<{ team: LiveFixture['homeTeam'], side: 'home' | 'away' }> = ({ team, side }) => {
    const imageKey = `${side}-${team.id}`;

    if (imageErrors[imageKey] || !team.logo) {
      return (
        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs font-bold text-gray-600">
          {team.name.substring(0, 2).toUpperCase()}
        </div>
      );
    }

    return (
      <div className="relative w-8 h-8">
        <Image
          src={team.logo}
          alt={`${team.name} logo`}
          fill
          className="object-contain rounded-full"
          onError={() => handleImageError(imageKey)}
          sizes="32px"
        />
      </div>
    );
  };

  if (compact) {
    return (
      <div
        className={`bg-white rounded-lg border border-gray-200 p-3 hover:shadow-md transition-shadow cursor-pointer ${className}`}
        onClick={handleCardClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 flex-1">
            <TeamLogo team={fixture.homeTeam} side="home" />
            <span className="text-sm font-medium truncate">{fixture.homeTeam.name}</span>
            <span className="text-lg font-bold text-gray-900">
              {fixture.score.home ?? '-'}
            </span>
          </div>

          <div className="px-2">
            <span className="text-gray-500">vs</span>
          </div>

          <div className="flex items-center space-x-2 flex-1 justify-end">
            <span className="text-lg font-bold text-gray-900">
              {fixture.score.away ?? '-'}
            </span>
            <span className="text-sm font-medium truncate">{fixture.awayTeam.name}</span>
            <TeamLogo team={fixture.awayTeam} side="away" />
          </div>
        </div>

        <div className="flex items-center justify-between mt-2">
          {getStatusDisplay()}
          <span className="text-xs text-gray-500">{fixture.league.name}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group ${className}`}
      onClick={handleCardClick}
    >
      {/* League Info */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          {fixture.league.logo && !imageErrors[`league-${fixture.league.id}`] ? (
            <div className="relative w-5 h-5">
              <Image
                src={fixture.league.logo}
                alt={`${fixture.league.name} logo`}
                fill
                className="object-contain"
                onError={() => handleImageError(`league-${fixture.league.id}`)}
                sizes="20px"
              />
            </div>
          ) : (
            <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
          )}
          <span className="text-sm font-medium text-gray-700">{fixture.league.name}</span>
        </div>
        {getStatusDisplay()}
      </div>

      {/* Teams and Score */}
      <div className="flex items-center justify-between mb-4">
        {/* Home Team */}
        <div className="flex items-center space-x-3 flex-1">
          <TeamLogo team={fixture.homeTeam} side="home" />
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {fixture.homeTeam.name}
            </h3>
          </div>
        </div>

        {/* Score */}
        <div className="flex items-center space-x-4 mx-6">
          <span className="text-2xl font-bold text-gray-900">
            {fixture.score.home ?? '-'}
          </span>
          <span className="text-gray-400">:</span>
          <span className="text-2xl font-bold text-gray-900">
            {fixture.score.away ?? '-'}
          </span>
        </div>

        {/* Away Team */}
        <div className="flex items-center space-x-3 flex-1 justify-end">
          <div className="flex-1 text-right">
            <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {fixture.awayTeam.name}
            </h3>
          </div>
          <TeamLogo team={fixture.awayTeam} side="away" />
        </div>
      </div>

      {/* Match Info */}
      <div className="flex items-center justify-between text-sm text-gray-500">
        <span>{getTimeDisplay()}</span>
        {fixture.venue && (
          <span>{fixture.venue.name}, {fixture.venue.city}</span>
        )}
      </div>

      {/* Hot Badge */}
      {fixture.isHot && (
        <div className="absolute top-2 right-2">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            🔥 Hot
          </span>
        </div>
      )}
    </div>
  );
};

export default LiveMatchCard;
