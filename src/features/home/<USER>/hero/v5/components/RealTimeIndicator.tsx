'use client';

import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';

interface RealTimeIndicatorProps {
  lastUpdate: Date | null;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  retryCount?: number;
  onRefresh?: () => void;
  className?: string;
}

const RealTimeIndicator: React.FC<RealTimeIndicatorProps> = ({
  lastUpdate,
  connectionStatus,
  retryCount = 0,
  onRefresh,
  className = ''
}) => {
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'connecting':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'disconnected':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return (
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        );
      case 'connecting':
        return (
          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-spin border border-yellow-600 border-t-transparent"></div>
        );
      case 'disconnected':
        return (
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
        );
      default:
        return (
          <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
        );
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Kết nối';
      case 'connecting':
        return 'Đang kết nối...';
      case 'disconnected':
        return retryCount > 0 ? `Mất kết nối (thử lại ${retryCount})` : 'Mất kết nối';
      default:
        return 'Không xác định';
    }
  };

  const getLastUpdateText = () => {
    if (!lastUpdate) return 'Chưa cập nhật';

    try {
      return `Cập nhật ${formatDistanceToNow(lastUpdate, { addSuffix: true, locale: vi })}`;
    } catch (err) {
      console.error('Date formatting error:', err);
      return 'Lỗi thời gian';
    }
  };

  return (
    <div className={`inline-flex items-center space-x-3 px-3 py-2 rounded-lg border ${getStatusColor()} ${className}`}>
      {/* Status Indicator */}
      <div className="flex items-center space-x-2">
        {getStatusIcon()}
        <span className="text-sm font-medium">
          {getStatusText()}
        </span>
      </div>

      {/* Last Update */}
      {lastUpdate && connectionStatus === 'connected' && (
        <>
          <div className="w-px h-4 bg-current opacity-30"></div>
          <span className="text-xs opacity-75">
            {getLastUpdateText()}
          </span>
        </>
      )}

      {/* Refresh Button */}
      {onRefresh && (
        <>
          <div className="w-px h-4 bg-current opacity-30"></div>
          <button
            onClick={onRefresh}
            disabled={connectionStatus === 'connecting'}
            className="text-xs hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
            title="Làm mới dữ liệu"
          >
            <svg
              className={`w-4 h-4 ${connectionStatus === 'connecting' ? 'animate-spin' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </>
      )}

      {/* Error Indicator */}
      {connectionStatus === 'disconnected' && retryCount > 0 && (
        <div className="flex items-center space-x-1">
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
      )}
    </div>
  );
};

export default RealTimeIndicator;
