'use client';

import React from 'react';
import Image from 'next/image';

// Design System - Professional UI Components

// Typography System
export const Typography = {
  H1: ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
    <h1 className={`text-5xl md:text-7xl lg:text-8xl font-bold leading-tight ${className}`}>
      {children}
    </h1>
  ),
  H2: ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
    <h2 className={`text-2xl md:text-3xl font-light ${className}`}>
      {children}
    </h2>
  ),
  Body: ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
    <p className={`text-lg md:text-xl leading-relaxed ${className}`}>
      {children}
    </p>
  ),
  Caption: ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
    <span className={`text-sm font-medium ${className}`}>
      {children}
    </span>
  )
};

// Button System
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  fullWidth?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  onClick,
  disabled = false,
  className = '',
  fullWidth = false
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-lg hover:shadow-xl',
    secondary: 'bg-white/10 text-white border border-white/20 hover:bg-white/20 focus:ring-white/50 backdrop-blur-sm',
    ghost: 'text-white hover:bg-white/10 focus:ring-white/50',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500'
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm rounded-lg',
    md: 'px-6 py-3 text-base rounded-xl',
    lg: 'px-8 py-4 text-lg rounded-2xl',
    xl: 'px-10 py-5 text-xl rounded-3xl'
  };

  const widthClass = fullWidth ? 'w-full' : '';

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`}
    >
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
};

// Card System
interface CardProps {
  children: React.ReactNode;
  variant?: 'glass' | 'solid' | 'outline';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  onClick?: () => void;
  hover?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'glass',
  padding = 'md',
  className = '',
  onClick,
  hover = true
}) => {
  const baseClasses = 'transition-all duration-300';

  const variantClasses = {
    glass: 'bg-white/5 backdrop-blur-xl border border-white/10',
    solid: 'bg-white shadow-lg',
    outline: 'border border-gray-200'
  };

  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10'
  };

  const hoverClasses = hover ? 'hover:scale-105 hover:shadow-2xl' : '';
  const cursorClass = onClick ? 'cursor-pointer' : '';

  return (
    <div
      onClick={onClick}
      className={`${baseClasses} ${variantClasses[variant]} ${paddingClasses[padding]} ${hoverClasses} ${cursorClass} rounded-3xl ${className}`}
    >
      {children}
    </div>
  );
};

// Status Indicator System
interface StatusIndicatorProps {
  status: 'live' | 'upcoming' | 'finished';
  count?: number;
  showPulse?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  count,
  showPulse = true,
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const dotSizes = {
    sm: 'w-1.5 h-1.5',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  };

  const statusConfig = {
    live: {
      color: 'bg-red-500/10 border-red-500/20 text-red-500',
      dotColor: 'bg-red-500',
      label: count ? `${count} trận đang diễn ra` : 'TRỰC TIẾP'
    },
    upcoming: {
      color: 'bg-blue-500/10 border-blue-500/20 text-blue-500',
      dotColor: 'bg-blue-500',
      label: count ? `${count} trận sắp diễn ra` : 'SẮP DIỄN RA'
    },
    finished: {
      color: 'bg-gray-500/10 border-gray-500/20 text-gray-500',
      dotColor: 'bg-gray-500',
      label: count ? `${count} trận đã kết thúc` : 'KẾT THÚC'
    }
  };

  const config = statusConfig[status];
  const pulseClass = showPulse && status === 'live' ? 'animate-pulse' : '';

  return (
    <div className={`inline-flex items-center space-x-2 backdrop-blur-sm border rounded-full ${config.color} ${sizeClasses[size]}`}>
      <div className={`${config.dotColor} rounded-full ${dotSizes[size]} ${pulseClass}`}></div>
      <span className="font-medium">{config.label}</span>
    </div>
  );
};

// Team Logo Component
interface TeamLogoProps {
  team: {
    name: string;
    logo?: string;
  };
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fallbackType?: 'initials' | 'icon';
}

export const TeamLogo: React.FC<TeamLogoProps> = ({
  team,
  size = 'md',
  fallbackType = 'initials'
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-12 h-12 text-sm',
    lg: 'w-16 h-16 text-base',
    xl: 'w-20 h-20 text-lg'
  };

  const [imageError, setImageError] = React.useState(false);

  if (team.logo && !imageError) {
    return (
      <div className={`relative ${sizeClasses[size]}`}>
        <Image
          src={team.logo}
          alt={`${team.name} logo`}
          fill
          className="object-contain rounded-full"
          onError={() => setImageError(true)}
        />
      </div>
    );
  }

  // Fallback
  return (
    <div className={`${sizeClasses[size]} bg-white/10 rounded-full flex items-center justify-center text-white font-bold border border-white/20`}>
      {fallbackType === 'initials' ? (
        team.name.substring(0, 2).toUpperCase()
      ) : (
        <svg className="w-1/2 h-1/2" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
        </svg>
      )}
    </div>
  );
};

// Match Score Component
interface MatchScoreProps {
  homeScore: number | null;
  awayScore: number | null;
  status: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showStatus?: boolean;
}

export const MatchScore: React.FC<MatchScoreProps> = ({
  homeScore,
  awayScore,
  status,
  size = 'md',
  showStatus = true
}) => {
  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-4xl',
    xl: 'text-6xl'
  };

  const isLive = status === 'LIVE';

  return (
    <div className="text-center">
      <div className={`font-bold text-white mb-2 ${sizeClasses[size]}`}>
        {homeScore ?? '-'} : {awayScore ?? '-'}
      </div>
      {showStatus && isLive && (
        <div className="text-green-400 text-sm animate-pulse">
          Đang cập nhật
        </div>
      )}
    </div>
  );
};

// Loading Skeleton
export const LoadingSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`animate-pulse bg-white/10 rounded-lg ${className}`}></div>
);

// Container System
export const Container: React.FC<{
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}> = ({ children, size = 'lg', className = '' }) => {
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  };

  return (
    <div className={`mx-auto px-4 ${sizeClasses[size]} ${className}`}>
      {children}
    </div>
  );
};
