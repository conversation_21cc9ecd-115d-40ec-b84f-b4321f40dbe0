'use client';

import React from 'react';
import HeroSectionV5 from './HeroSectionV5';
import { useLeagues } from './hooks/useLeagues';
import { HeroV5Props } from './types';

interface HeroSectionV5ContainerProps extends Omit<HeroV5Props, 'featuredLeagues'> {
  // Additional container-specific props
  className?: string;
}

const HeroSectionV5Container: React.FC<HeroSectionV5ContainerProps> = (props) => {
  // Fetch featured leagues
  const { leagues: featuredLeagues, loading: leaguesLoading } = useLeagues({
    onlyHot: true,
    limit: 20
  });

  // Show loading state while leagues are being fetched
  if (leaguesLoading && featuredLeagues.length === 0) {
    return (
      <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600"><PERSON><PERSON> tải dữ liệu...</p>
        </div>
      </section>
    );
  }

  return (
    <HeroSectionV5
      {...props}
      featuredLeagues={featuredLeagues}
    />
  );
};

export default HeroSectionV5Container;
