'use client';

import React from 'react';
import HeroSectionV5New from './HeroSectionV5New';
import { useLeagues } from './hooks/useLeagues';
import { useRealTimeFixtures } from './hooks/useRealTimeFixtures';
import { HeroV5Props } from './types';

interface HeroSectionV5ContainerProps extends Omit<HeroV5Props, 'featuredLeagues' | 'featuredMatch' | 'liveMatches'> {
  // Additional container-specific props
  className?: string;
}

const HeroSectionV5Container: React.FC<HeroSectionV5ContainerProps> = (props) => {
  // Fetch featured leagues
  const { leagues: featuredLeagues, loading: leaguesLoading } = useLeagues({
    onlyHot: true,
    limit: 10
  });

  // Fetch live and upcoming matches
  const { fixtures: liveMatches, loading: matchesLoading } = useRealTimeFixtures(
    { status: 'LIVE,UPCOMING' },
    { autoRefresh: true, refreshInterval: 10000 }
  );

  // Get featured match (first live match or most important upcoming)
  const featuredMatch = liveMatches.find(match => match.status === 'LIVE') ||
    liveMatches.find(match => match.isHot) ||
    liveMatches[0];

  // Show loading state while data is being fetched
  if ((leaguesLoading && featuredLeagues.length === 0) || (matchesLoading && liveMatches.length === 0)) {
    return (
      <section className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-white">Đang tải trải nghiệm bóng đá...</p>
        </div>
      </section>
    );
  }

  return (
    <HeroSectionV5New
      {...props}
      featuredLeagues={featuredLeagues}
      featuredMatch={featuredMatch}
      liveMatches={liveMatches.filter(match => match.status === 'LIVE')}
    />
  );
};

export default HeroSectionV5Container;
