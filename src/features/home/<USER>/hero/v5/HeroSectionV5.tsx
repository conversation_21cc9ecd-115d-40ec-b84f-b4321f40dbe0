'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { HeroV5Props, FilterState, ViewMode, LiveFixture } from './types';
import { useRealTimeFixtures } from './hooks/useRealTimeFixtures';
import LiveMatchCard from './components/LiveMatchCard';
import FilterPanel from './components/FilterPanel';
import RealTimeIndicator from './components/RealTimeIndicator';

const HeroSectionV5: React.FC<HeroV5Props> = ({
  initialFixtures = [],
  featuredLeagues = [],
  autoRefresh = true,
  refreshInterval = 10000,
  maxFixtures = 20,
  showBroadcastLinks = false
}) => {
  const [filters, setFilters] = useState<FilterState>({});
  const [viewMode, setViewMode] = useState<ViewMode>({ type: 'grid', density: 'comfortable' });
  const [selectedFixture, setSelectedFixture] = useState<LiveFixture | null>(null);

  // Suppress unused variable warning for future modal implementation
  void selectedFixture;

  // Real-time fixtures hook
  const {
    fixtures,
    loading,
    error,
    lastUpdate,
    retryCount,
    connectionStatus,
    refetch
  } = useRealTimeFixtures(filters, {
    autoRefresh,
    refreshInterval,
    enableWebSocket: true
  });

  // Filtered and sorted fixtures
  const displayFixtures = useMemo(() => {
    let filtered = fixtures.length > 0 ? fixtures : initialFixtures;

    // Apply additional client-side filtering if needed
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(fixture =>
        fixture.homeTeam.name.toLowerCase().includes(searchTerm) ||
        fixture.awayTeam.name.toLowerCase().includes(searchTerm)
      );
    }

    // Sort by priority: LIVE > UPCOMING > FT
    filtered.sort((a, b) => {
      const statusPriority = { 'LIVE': 3, 'HT': 2, 'UPCOMING': 1, 'FT': 0 };
      const aPriority = statusPriority[a.status] || 0;
      const bPriority = statusPriority[b.status] || 0;

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // Secondary sort by date
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });

    return filtered.slice(0, maxFixtures);
  }, [fixtures, initialFixtures, filters, maxFixtures]);

  const handleFiltersChange = useCallback((newFilters: FilterState) => {
    setFilters(newFilters);
  }, []);

  const handleFixtureClick = useCallback((fixture: LiveFixture) => {
    setSelectedFixture(fixture);
    // Could open modal, navigate to detail page, etc.
    console.log('Fixture clicked:', fixture);
  }, []);

  const handleViewModeChange = useCallback((newViewMode: ViewMode) => {
    setViewMode(newViewMode);
  }, []);

  const getGridClasses = () => {
    const { type, density } = viewMode;

    if (type === 'list') {
      return 'space-y-4';
    }

    if (type === 'compact') {
      return density === 'dense'
        ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3'
        : 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
    }

    // Grid mode
    return density === 'comfortable'
      ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
      : density === 'compact'
        ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
        : 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-3';
  };

  const liveCount = displayFixtures.filter(f => f.status === 'LIVE' || f.status === 'HT').length;
  const upcomingCount = displayFixtures.filter(f => f.status === 'UPCOMING').length;

  return (
    <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Kết Quả Bóng Đá
            <span className="text-blue-600"> Trực Tiếp</span>
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Theo dõi tỷ số và thông tin trận đấu cập nhật liên tục
          </p>

          {/* Stats */}
          <div className="flex items-center justify-center space-x-6 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{liveCount}</div>
              <div className="text-sm text-gray-500">Đang diễn ra</div>
            </div>
            <div className="w-px h-8 bg-gray-300"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{upcomingCount}</div>
              <div className="text-sm text-gray-500">Sắp diễn ra</div>
            </div>
            <div className="w-px h-8 bg-gray-300"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{displayFixtures.length}</div>
              <div className="text-sm text-gray-500">Tổng trận</div>
            </div>
          </div>

          {/* Real-time Indicator */}
          <div className="flex justify-center mb-6">
            <RealTimeIndicator
              lastUpdate={lastUpdate}
              connectionStatus={connectionStatus}
              retryCount={retryCount}
              onRefresh={refetch}
            />
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-col lg:flex-row gap-6 mb-8">
          {/* Filters */}
          <div className="lg:w-80">
            <FilterPanel
              filters={filters}
              onFiltersChange={handleFiltersChange}
              leagues={featuredLeagues}
            />
          </div>

          {/* View Mode Controls */}
          <div className="flex-1">
            <div className="bg-white rounded-xl border border-gray-200 p-4 mb-6">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900">Hiển thị</h3>
                <div className="flex items-center space-x-2">
                  {/* View Type */}
                  <div className="flex rounded-lg border border-gray-200 p-1">
                    {(['grid', 'list', 'compact'] as const).map(type => (
                      <button
                        key={type}
                        onClick={() => handleViewModeChange({ ...viewMode, type })}
                        className={`px-3 py-1 text-sm rounded transition-colors ${viewMode.type === type
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-600 hover:text-gray-900'
                          }`}
                      >
                        {type === 'grid' ? 'Lưới' : type === 'list' ? 'Danh sách' : 'Gọn'}
                      </button>
                    ))}
                  </div>

                  {/* Density */}
                  <div className="flex rounded-lg border border-gray-200 p-1">
                    {(['comfortable', 'compact', 'dense'] as const).map(density => (
                      <button
                        key={density}
                        onClick={() => handleViewModeChange({ ...viewMode, density })}
                        className={`px-2 py-1 text-xs rounded transition-colors ${viewMode.density === density
                            ? 'bg-gray-100 text-gray-700'
                            : 'text-gray-500 hover:text-gray-700'
                          }`}
                      >
                        {density === 'comfortable' ? 'Rộng' : density === 'compact' ? 'Vừa' : 'Gọn'}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Content */}
            {loading && displayFixtures.length === 0 ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Đang tải dữ liệu...</p>
              </div>
            ) : error && displayFixtures.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-red-500 mb-4">
                  <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <p className="text-gray-600 mb-4">Không thể tải dữ liệu</p>
                <button
                  onClick={refetch}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Thử lại
                </button>
              </div>
            ) : displayFixtures.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-600">Không có trận đấu nào phù hợp với bộ lọc</p>
              </div>
            ) : (
              <div className={getGridClasses()}>
                {displayFixtures.map(fixture => (
                  <LiveMatchCard
                    key={fixture.id}
                    fixture={fixture}
                    onClick={handleFixtureClick}
                    showBroadcastLinks={showBroadcastLinks}
                    compact={viewMode.type === 'compact' || viewMode.type === 'list'}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSectionV5;
