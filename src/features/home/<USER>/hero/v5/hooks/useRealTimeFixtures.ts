'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { LiveFixture, FilterState, FixtureUpdateEvent } from '../types';

interface UseRealTimeFixturesOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  maxRetries?: number;
  enableWebSocket?: boolean;
}

interface UseRealTimeFixturesReturn {
  fixtures: LiveFixture[];
  loading: boolean;
  error: string | null;
  lastUpdate: Date | null;
  retryCount: number;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  refetch: () => Promise<void>;
  updateFixture: (fixtureId: number, updates: Partial<LiveFixture>) => void;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export const useRealTimeFixtures = (
  filters: FilterState = {},
  options: UseRealTimeFixturesOptions = {}
): UseRealTimeFixturesReturn => {
  const {
    autoRefresh = true,
    refreshInterval = 10000, // 10 seconds
    maxRetries = 3,
    enableWebSocket = false
  } = options;

  const [fixtures, setFixtures] = useState<LiveFixture[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Fetch fixtures from API
  const fetchFixtures = useCallback(async () => {
    try {
      setConnectionStatus('connecting');

      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();

      const params = new URLSearchParams();
      if (filters.league) params.append('leagueId', filters.league.toString());
      if (filters.status) params.append('status', filters.status);
      if (filters.search) params.append('search', filters.search);

      const response = await fetch(
        `${API_BASE_URL}/football/fixtures/upcoming-and-live?${params.toString()}`,
        {
          signal: abortControllerRef.current.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      setFixtures(data.data || []);
      setLastUpdate(new Date());
      setError(null);
      setRetryCount(0);
      setConnectionStatus('connected');

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return; // Request was cancelled, ignore
      }

      console.error('Failed to fetch fixtures:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setConnectionStatus('disconnected');

      // Retry logic
      if (retryCount < maxRetries) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => fetchFixtures(), Math.pow(2, retryCount) * 1000);
      }
    } finally {
      setLoading(false);
    }
  }, [filters, retryCount, maxRetries]);

  // Manual refetch
  const refetch = useCallback(async () => {
    setLoading(true);
    setRetryCount(0);
    await fetchFixtures();
  }, [fetchFixtures]);

  // Update specific fixture
  const updateFixture = useCallback((fixtureId: number, updates: Partial<LiveFixture>) => {
    setFixtures(prev =>
      prev.map(fixture =>
        fixture.id === fixtureId
          ? { ...fixture, ...updates }
          : fixture
      )
    );
    setLastUpdate(new Date());
  }, []);

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (!enableWebSocket || typeof window === 'undefined') return;

    const connectWebSocket = () => {
      try {
        const wsUrl = `${API_BASE_URL.replace('http', 'ws')}/ws/fixtures`;
        wsRef.current = new WebSocket(wsUrl);

        wsRef.current.onopen = () => {
          console.log('WebSocket connected');
          setConnectionStatus('connected');
        };

        wsRef.current.onmessage = (event) => {
          try {
            const update: FixtureUpdateEvent = JSON.parse(event.data);
            updateFixture(update.fixtureId, update.data);
          } catch (err) {
            console.error('Failed to parse WebSocket message:', err);
          }
        };

        wsRef.current.onclose = () => {
          console.log('WebSocket disconnected');
          setConnectionStatus('disconnected');

          // Reconnect after 5 seconds
          setTimeout(connectWebSocket, 5000);
        };

        wsRef.current.onerror = (error) => {
          console.error('WebSocket error:', error);
          setConnectionStatus('disconnected');
        };

      } catch (err) {
        console.error('Failed to connect WebSocket:', err);
        setConnectionStatus('disconnected');
      }
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [enableWebSocket, updateFixture]);

  // Auto-refresh polling
  useEffect(() => {
    if (!autoRefresh) return;

    // Initial fetch
    fetchFixtures();

    // Set up interval for polling
    intervalRef.current = setInterval(fetchFixtures, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [autoRefresh, refreshInterval, fetchFixtures]);

  // Handle visibility change to pause/resume updates
  useEffect(() => {
    if (typeof document === 'undefined') return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Pause updates when tab is not visible
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      } else {
        // Resume updates when tab becomes visible
        if (autoRefresh) {
          fetchFixtures(); // Immediate update
          intervalRef.current = setInterval(fetchFixtures, refreshInterval);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [autoRefresh, refreshInterval, fetchFixtures]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  return {
    fixtures,
    loading,
    error,
    lastUpdate,
    retryCount,
    connectionStatus,
    refetch,
    updateFixture,
  };
};
