'use client';

import { useState, useEffect, useCallback } from 'react';
import { League } from '../types';

interface UseLeaguesOptions {
  autoFetch?: boolean;
  onlyHot?: boolean;
  limit?: number;
}

interface UseLeaguesReturn {
  leagues: League[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export const useLeagues = (options: UseLeaguesOptions = {}): UseLeaguesReturn => {
  const { autoFetch = true, onlyHot = false, limit } = options;
  
  const [leagues, setLeagues] = useState<League[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchLeagues = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (onlyHot) params.append('isHot', 'true');
      if (limit) params.append('limit', limit.toString());
      params.append('active', 'true'); // Only active leagues

      const response = await fetch(
        `${API_BASE_URL}/football/leagues?${params.toString()}`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setLeagues(data.data || []);
      
    } catch (err) {
      console.error('Failed to fetch leagues:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [onlyHot, limit]);

  const refetch = useCallback(async () => {
    await fetchLeagues();
  }, [fetchLeagues]);

  useEffect(() => {
    if (autoFetch) {
      fetchLeagues();
    }
  }, [autoFetch, fetchLeagues]);

  return {
    leagues,
    loading,
    error,
    refetch,
  };
};
