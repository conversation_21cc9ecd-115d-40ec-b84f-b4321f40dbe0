// HeroSectionV5 Types - Interactive Sports Experience
export interface LiveFixture {
  id: number;
  externalId: number;
  date: string;
  status: 'UPCOMING' | 'LIVE' | 'FT' | 'HT' | 'NS';
  homeTeam: {
    id: number;
    name: string;
    logo?: string;
  };
  awayTeam: {
    id: number;
    name: string;
    logo?: string;
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo?: string;
  };
  venue?: {
    name: string;
    city: string;
  };
  score: {
    home: number | null;
    away: number | null;
  };
  isHot?: boolean;
  thumb?: string;
}

export interface FixtureStatistic {
  team: {
    id: number;
    name: string;
  };
  statistics: Array<{
    type: string;
    value: string | number;
  }>;
}

export interface League {
  id: number;
  externalId: number;
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  season: number;
  active: boolean;
  isHot?: boolean;
}

export interface BroadcastLink {
  id: number;
  linkName: string;
  linkUrl: string;
  linkComment: string;
  language?: string;
  quality?: string;
}

export interface HeroV5Props {
  // Hero content
  title?: string;
  subtitle?: string;

  // Featured content
  featuredMatch?: LiveFixture;
  liveMatches?: LiveFixture[];
  featuredLeagues?: League[];

  // Interactive features
  enableLiveUpdates?: boolean;
  showMatchHighlights?: boolean;
  enableNotifications?: boolean;

  // Visual settings
  theme?: 'light' | 'dark' | 'auto';
  backgroundVideo?: string;
  heroImage?: string;

  // Call to actions
  primaryCTA?: {
    label: string;
    href: string;
    variant?: 'primary' | 'secondary';
  };
  secondaryCTA?: {
    label: string;
    href: string;
    variant?: 'primary' | 'secondary';
  };
}

export interface FilterState {
  league?: number;
  status?: string;
  search?: string;
  team?: number;
}

export interface ViewMode {
  type: 'grid' | 'list' | 'compact';
  density: 'comfortable' | 'compact' | 'dense';
}

// Real-time update events
export interface FixtureUpdateEvent {
  type: 'SCORE_UPDATE' | 'STATUS_CHANGE' | 'NEW_FIXTURE' | 'FIXTURE_ENDED';
  fixtureId: number;
  data: Partial<LiveFixture>;
  timestamp: string;
}

// Performance monitoring
export interface PerformanceMetrics {
  apiResponseTime: number;
  renderTime: number;
  updateFrequency: number;
  errorRate: number;
}

// Accessibility
export interface A11ySettings {
  reduceMotion: boolean;
  highContrast: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
}
