// HeroSectionV5 Types - Professional UI/UX Design
export interface LiveFixture {
  id: number;
  externalId: number;
  date: string;
  status: 'UPCOMING' | 'LIVE' | 'FT' | 'HT' | 'NS';
  homeTeam: {
    id: number;
    name: string;
    logo?: string;
  };
  awayTeam: {
    id: number;
    name: string;
    logo?: string;
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo?: string;
  };
  venue?: {
    name: string;
    city: string;
  };
  score: {
    home: number | null;
    away: number | null;
  };
  isHot?: boolean;
  thumb?: string;
}

export interface FixtureStatistic {
  team: {
    id: number;
    name: string;
  };
  statistics: Array<{
    type: string;
    value: string | number;
  }>;
}

export interface League {
  id: number;
  externalId: number;
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  season: number;
  active: boolean;
  isHot?: boolean;
}

export interface BroadcastLink {
  id: number;
  linkName: string;
  linkUrl: string;
  linkComment: string;
  language?: string;
  quality?: string;
}

// Modern Hero Section Props with UX focus
export interface HeroV5Props {
  // Content Strategy
  headline?: string;
  subheadline?: string;
  description?: string;

  // Visual Design
  variant?: 'minimal' | 'immersive' | 'dynamic';
  colorScheme?: 'light' | 'dark' | 'auto' | 'brand';
  backgroundType?: 'gradient' | 'image' | 'video' | 'pattern';
  backgroundMedia?: string;

  // Interactive Elements
  showLiveScore?: boolean;
  showTrendingMatches?: boolean;
  showQuickActions?: boolean;
  enableAnimations?: boolean;

  // Call to Actions (UX optimized)
  primaryAction?: {
    label: string;
    href: string;
    icon?: string;
    variant?: 'solid' | 'outline' | 'ghost';
    size?: 'sm' | 'md' | 'lg';
  };
  secondaryAction?: {
    label: string;
    href: string;
    icon?: string;
    variant?: 'solid' | 'outline' | 'ghost';
    size?: 'sm' | 'md' | 'lg';
  };

  // Data Integration
  featuredMatch?: LiveFixture;
  trendingMatches?: LiveFixture[];
  featuredLeagues?: League[];

  // UX Settings
  autoPlay?: boolean;
  showProgress?: boolean;
  enableKeyboardNav?: boolean;
  reducedMotion?: boolean;
}

export interface FilterState {
  league?: number;
  status?: string;
  search?: string;
  team?: number;
}

export interface ViewMode {
  type: 'grid' | 'list' | 'compact';
  density: 'comfortable' | 'compact' | 'dense';
}

// Real-time update events
export interface FixtureUpdateEvent {
  type: 'SCORE_UPDATE' | 'STATUS_CHANGE' | 'NEW_FIXTURE' | 'FIXTURE_ENDED';
  fixtureId: number;
  data: Partial<LiveFixture>;
  timestamp: string;
}

// Performance monitoring
export interface PerformanceMetrics {
  apiResponseTime: number;
  renderTime: number;
  updateFrequency: number;
  errorRate: number;
}

// Accessibility
export interface A11ySettings {
  reduceMotion: boolean;
  highContrast: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
}
