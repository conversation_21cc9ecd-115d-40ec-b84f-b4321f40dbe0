// Enhanced Audio System for Sports Arena
import { useState, useEffect, useCallback } from 'react';

export interface AudioAsset {
        id: string;
        url: string;
        volume: number;
        loop: boolean;
        category: 'ambience' | 'ui' | 'celebration' | 'notification' | 'match';
        preload: boolean;
        fadeDuration: number;
}

export interface AudioSystemConfig {
        masterVolume: number;
        enabledCategories: string[];
        maxConcurrentSounds: number;
        enableSpatialAudio: boolean;
        enableHapticFeedback: boolean;
        audioQuality: 'low' | 'medium' | 'high';
}

export interface SpatialAudioConfig {
        x: number;
        y: number;
        z: number;
        maxDistance: number;
        rolloffFactor: number;
}

class EnhancedAudioManager {
        private context: AudioContext | null = null;
        private masterGain: GainNode | null = null;
        private compressor: DynamicsCompressorNode | null = null;
        private reverb: ConvolverNode | null = null;
        private audioAssets: Map<string, HTMLAudioElement> = new Map();
        private playingSounds: Map<string, { audio: HTMLAudioElement; gainNode: GainNode }> = new Map();
        private config: AudioSystemConfig;

        constructor(config: AudioSystemConfig) {
                this.config = config;
                this.initialize();
        }

        private async initialize() {
                try {
                        this.context = new (window.AudioContext || (window as Window & typeof globalThis & { webkitAudioContext?: AudioContext }).webkitAudioContext)();

                        // Create master gain node
                        this.masterGain = this.context.createGain();
                        this.masterGain.gain.value = this.config.masterVolume;

                        // Create compressor for dynamic range control
                        this.compressor = this.context.createDynamicsCompressor();
                        this.compressor.threshold.value = -24;
                        this.compressor.knee.value = 30;
                        this.compressor.ratio.value = 12;
                        this.compressor.attack.value = 0.003;
                        this.compressor.release.value = 0.25;

                        // Create reverb for spatial effects
                        if (this.config.enableSpatialAudio) {
                                this.reverb = this.context.createConvolver();
                                await this.createReverbImpulse();
                        }

                        // Connect audio chain
                        if (this.reverb) {
                                this.masterGain.connect(this.reverb);
                                this.reverb.connect(this.compressor);
                        } else {
                                this.masterGain.connect(this.compressor);
                        }

                        this.compressor.connect(this.context.destination);

                } catch (error) {
                        console.error('Failed to initialize audio system:', error);
                }
        }

        private async createReverbImpulse() {
                if (!this.context || !this.reverb) return;

                const length = this.context.sampleRate * 2; // 2 seconds
                const impulse = this.context.createBuffer(2, length, this.context.sampleRate);

                for (let channel = 0; channel < 2; channel++) {
                        const channelData = impulse.getChannelData(channel);
                        for (let i = 0; i < length; i++) {
                                const decay = Math.pow(1 - i / length, 2);
                                channelData[i] = (Math.random() * 2 - 1) * decay * 0.1;
                        }
                }

                this.reverb.buffer = impulse;
        }

        async preloadAsset(asset: AudioAsset): Promise<void> {
                return new Promise((resolve, reject) => {
                        const audio = new Audio();
                        audio.preload = 'auto';
                        audio.crossOrigin = 'anonymous';

                        audio.addEventListener('canplaythrough', () => {
                                this.audioAssets.set(asset.id, audio);
                                resolve();
                        });

                        audio.addEventListener('error', (error) => {
                                console.error(`Failed to load audio asset ${asset.id}:`, error);
                                reject(error);
                        });

                        // Fallback to generated audio if URL fails
                        audio.src = asset.url || this.generateFallbackAudio(asset.category);
                });
        }

        private generateFallbackAudio(category: AudioAsset['category']): string {
                if (!this.context) return '';

                const oscillator = this.context.createOscillator();
                // const audioGainNode = this.context.createGain();

                const frequencies = {
                        ambience: 220,
                        ui: 800,
                        celebration: 440,
                        notification: 1000,
                        match: 330
                };

                oscillator.frequency.value = frequencies[category];
                oscillator.type = category === 'celebration' ? 'sawtooth' : 'sine';

                // Create a simple melody for fallback
                const buffer = this.context.createBuffer(1, this.context.sampleRate * 2, this.context.sampleRate);
                const data = buffer.getChannelData(0);

                for (let i = 0; i < data.length; i++) {
                        const t = i / this.context.sampleRate;
                        data[i] = Math.sin(2 * Math.PI * frequencies[category] * t) * Math.exp(-t * 0.5) * 0.3;
                }

                // Convert buffer to data URL
                const url = URL.createObjectURL(new Blob([buffer], { type: 'audio/wav' }));
                return url;
        }

        async play(
                assetId: string,
                options: {
                        volume?: number;
                        playbackRate?: number;
                        loop?: boolean;
                        fadeIn?: number;
                        spatial?: SpatialAudioConfig;
                        haptic?: boolean;
                } = {}
        ): Promise<string> {
                if (!this.context || !this.masterGain) return '';

                const audio = this.audioAssets.get(assetId);
                if (!audio) return '';

                // Resume audio context if suspended
                if (this.context.state === 'suspended') {
                        await this.context.resume();
                }

                try {
                        const audioClone = audio.cloneNode() as HTMLAudioElement;
                        const source = this.context.createMediaElementSource(audioClone);
                        const gainNode = this.context.createGain();

                        // Configure playback
                        audioClone.volume = options.volume || 1;
                        audioClone.playbackRate = options.playbackRate || 1;
                        audioClone.loop = options.loop || false;

                        // Set initial gain
                        const targetGain = options.volume || 1;
                        gainNode.gain.value = options.fadeIn ? 0 : targetGain;

                        // Connect audio graph
                        source.connect(gainNode);

                        // Apply spatial audio if enabled
                        if (options.spatial && this.config.enableSpatialAudio) {
                                const panner = this.context.createPanner();
                                panner.panningModel = 'HRTF';
                                panner.distanceModel = 'inverse';
                                panner.maxDistance = options.spatial.maxDistance;
                                panner.rolloffFactor = options.spatial.rolloffFactor;
                                panner.setPosition(options.spatial.x, options.spatial.y, options.spatial.z);

                                gainNode.connect(panner);
                                panner.connect(this.masterGain);
                        } else {
                                gainNode.connect(this.masterGain);
                        }

                        // Generate unique ID for this sound instance
                        const soundId = `${assetId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

                        // Store playing sound reference
                        this.playingSounds.set(soundId, { audio: audioClone, gainNode });

                        // Fade in if specified
                        if (options.fadeIn) {
                                gainNode.gain.exponentialRampToValueAtTime(
                                        targetGain,
                                        this.context.currentTime + options.fadeIn / 1000
                                );
                        }

                        // Haptic feedback
                        if (options.haptic && this.config.enableHapticFeedback && 'vibrate' in navigator) {
                                const patterns = {
                                        ui: [50],
                                        celebration: [100, 50, 100, 50, 200],
                                        notification: [200],
                                        goal: [300, 100, 300],
                                        match: [150]
                                };

                                const category = this.getAssetCategory(assetId);
                                if (category && patterns[category as keyof typeof patterns]) {
                                        navigator.vibrate(patterns[category as keyof typeof patterns]);
                                }
                        }

                        // Clean up when audio ends
                        audioClone.addEventListener('ended', () => {
                                this.stop(soundId);
                        });

                        // Start playback
                        await audioClone.play();

                        return soundId;
                } catch (error) {
                        console.error(`Failed to play audio ${assetId}:`, error);
                        return '';
                }
        }

        stop(soundId: string, fadeOut: number = 0): void {
                const sound = this.playingSounds.get(soundId);
                if (!sound || !this.context) return;

                if (fadeOut > 0) {
                        sound.gainNode.gain.exponentialRampToValueAtTime(
                                0.001,
                                this.context.currentTime + fadeOut / 1000
                        );

                        setTimeout(() => {
                                sound.audio.pause();
                                this.playingSounds.delete(soundId);
                        }, fadeOut);
                } else {
                        sound.audio.pause();
                        this.playingSounds.delete(soundId);
                }
        }

        stopAll(category?: string): void {
                this.playingSounds.forEach((sound, soundId) => {
                        if (!category || this.getAssetCategory(soundId)?.includes(category)) {
                                this.stop(soundId, 500);
                        }
                });
        }

        setMasterVolume(volume: number): void {
                this.config.masterVolume = Math.max(0, Math.min(1, volume));
                if (this.masterGain) {
                        this.masterGain.gain.value = this.config.masterVolume;
                }
        }

        private getAssetCategory(assetId: string): string | undefined {
                // Extract category from asset ID (assumes format: category_name)
                return assetId.split('_')[0];
        }

        // Advanced audio analysis
        createAnalyser(soundId: string): AnalyserNode | null {
                const sound = this.playingSounds.get(soundId);
                if (!sound || !this.context) return null;

                const analyser = this.context.createAnalyser();
                analyser.fftSize = 256;
                analyser.smoothingTimeConstant = 0.8;

                // Insert analyser into audio chain
                sound.gainNode.disconnect();
                sound.gainNode.connect(analyser);
                analyser.connect(this.masterGain);

                return analyser;
        }
}

export const useEnhancedAudioSystem = (config: AudioSystemConfig) => {
        const [audioManager] = useState(() => new EnhancedAudioManager(config));
        const [isInitialized, setIsInitialized] = useState(false);
        const [loadedAssets, setLoadedAssets] = useState<Set<string>>(new Set());
        const [playingSounds, setPlayingSounds] = useState<Set<string>>(new Set());

        // Initialize and preload assets
        useEffect(() => {
                // Audio assets for the sports arena
                const defaultAssets: AudioAsset[] = [
                        {
                                id: 'ambience_stadium',
                                url: '', // Will use generated fallback
                                volume: 0.3,
                                loop: true,
                                category: 'ambience',
                                preload: true,
                                fadeDuration: 2000
                        },
                        {
                                id: 'ui_click',
                                url: '',
                                volume: 0.5,
                                loop: false,
                                category: 'ui',
                                preload: true,
                                fadeDuration: 0
                        },
                        {
                                id: 'ui_hover',
                                url: '',
                                volume: 0.3,
                                loop: false,
                                category: 'ui',
                                preload: true,
                                fadeDuration: 0
                        },
                        {
                                id: 'celebration_goal',
                                url: '',
                                volume: 0.8,
                                loop: false,
                                category: 'celebration',
                                preload: true,
                                fadeDuration: 500
                        },
                        {
                                id: 'notification_live',
                                url: '',
                                volume: 0.6,
                                loop: false,
                                category: 'notification',
                                preload: true,
                                fadeDuration: 200
                        },
                        {
                                id: 'match_whistle',
                                url: '',
                                volume: 0.7,
                                loop: false,
                                category: 'match',
                                preload: true,
                                fadeDuration: 100
                        }
                ];

                const initializeAudio = async () => {
                        try {
                                const promises = defaultAssets
                                        .filter(asset => asset.preload && config.enabledCategories.includes(asset.category))
                                        .map(asset => audioManager.preloadAsset(asset));

                                await Promise.all(promises);

                                setLoadedAssets(new Set(defaultAssets.map(asset => asset.id)));
                                setIsInitialized(true);
                        } catch (error) {
                                console.error('Failed to initialize audio system:', error);
                        }
                };

                initializeAudio();
        }, [audioManager, config.enabledCategories]);

        // Play audio with options
        const playAudio = useCallback(async (
                assetId: string,
                options?: Parameters<typeof audioManager.play>[1]
        ) => {
                if (!isInitialized || !loadedAssets.has(assetId)) return '';

                const soundId = await audioManager.play(assetId, options);
                if (soundId) {
                        setPlayingSounds(current => new Set([...current, soundId]));
                }
                return soundId;
        }, [audioManager, isInitialized, loadedAssets]);

        // Stop audio
        const stopAudio = useCallback((soundId: string, fadeOut?: number) => {
                audioManager.stop(soundId, fadeOut);
                setPlayingSounds(current => {
                        const updated = new Set(current);
                        updated.delete(soundId);
                        return updated;
                });
        }, [audioManager]);

        // Specialized audio functions
        const playUISound = useCallback((type: 'click' | 'hover') => {
                return playAudio(`ui_${type}`, { volume: 0.3, haptic: true });
        }, [playAudio]);

        const playGoalCelebration = useCallback(() => {
                return playAudio('celebration_goal', {
                        volume: 0.8,
                        haptic: true,
                        fadeIn: 200
                });
        }, [playAudio]);

        const playLiveNotification = useCallback(() => {
                return playAudio('notification_live', {
                        volume: 0.6,
                        haptic: true
                });
        }, [playAudio]);

        const setMasterVolume = useCallback((volume: number) => {
                audioManager.setMasterVolume(volume);
        }, [audioManager]);

        const stopAllAudio = useCallback((category?: string) => {
                audioManager.stopAll(category);
                if (category) {
                        setPlayingSounds(current =>
                                new Set([...current].filter(soundId =>
                                        !audioManager['getAssetCategory'](soundId)?.includes(category)
                                ))
                        );
                } else {
                        setPlayingSounds(new Set());
                }
        }, [audioManager]);

        return {
                isInitialized,
                loadedAssets: Array.from(loadedAssets),
                playingSounds: Array.from(playingSounds),
                playAudio,
                stopAudio,
                playUISound,
                playGoalCelebration,
                playLiveNotification,
                setMasterVolume,
                stopAllAudio,
                audioManager
        };
};
