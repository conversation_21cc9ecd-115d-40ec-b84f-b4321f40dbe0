'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useMagneticFieldSystem } from '../hooks/useMagneticFieldSystem';
import { MagneticFieldRenderer } from './MagneticFieldRenderer';
import { MagneticFieldDebugPanel } from './MagneticFieldDebugPanel';
import type { AudioSystemIntegration, ParticleSystemIntegration } from '../hooks/useMagneticFieldSystem';

interface MagneticFieldDemoProps {
  width?: number;
  height?: number;
  className?: string;
}

export const MagneticFieldDemo: React.FC<MagneticFieldDemoProps> = ({
  width = 800,
  height = 600,
  className = ''
}) => {
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  const [config, setConfig] = useState({
    maxFields: 25,
    enableVisualization: true,
    fieldLifespan: 8000,
    defaultStrength: 100,
    interactionThreshold: 0.15,
    enableFieldInteractions: true,
    performanceMode: false,
    accessibilityMode: false,
    mobileOptimization: false,
    autoDetectMobile: true,
    batteryOptimization: true,
    enableDebugMode: true,
    debugUpdateInterval: 1000
  });

  const containerRef = useRef<HTMLDivElement>(null);

  // Mock audio system integration
  const audioSystem: AudioSystemIntegration = {
    playFieldCreation: useCallback((fieldType, strength) => {
      // Mock audio implementation
      console.log(`🔊 Playing ${fieldType} field creation sound with strength ${strength}`);
      
      // In a real implementation, you would play actual audio
      if ('AudioContext' in window) {
        try {
          const audioContext = new AudioContext();
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();
          
          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);
          
          oscillator.frequency.setValueAtTime(
            fieldType === 'attract' ? 440 : 220, 
            audioContext.currentTime
          );
          oscillator.type = 'sine';
          
          gainNode.gain.setValueAtTime(0, audioContext.currentTime);
          gainNode.gain.linearRampToValueAtTime(0.1 * (strength / 100), audioContext.currentTime + 0.01);
          gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3);
          
          oscillator.start(audioContext.currentTime);
          oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
          console.log('Audio not available:', error);
        }
      }
    }, []),

    playFieldInteraction: useCallback((interactionStrength, fieldTypes) => {
      console.log(`🔊 Playing interaction sound: strength ${interactionStrength}, types: ${fieldTypes.join(', ')}`);
    }, []),

    playFieldDestruction: useCallback((fieldType) => {
      console.log(`🔊 Playing ${fieldType} field destruction sound`);
    }, []),

    setAmbientFieldVolume: useCallback((fieldCount) => {
      console.log(`🔊 Setting ambient volume based on ${fieldCount} fields`);
    }, [])
  };

  // Mock particle system integration
  const particleSystem: ParticleSystemIntegration = {
    createFieldParticles: useCallback((field) => {
      console.log(`✨ Creating particles for ${field.type} field at (${field.x}, ${field.y})`);
      // In a real implementation, you would create actual particle effects
    }, []),

    updateFieldParticles: useCallback((fieldId, field) => {
      console.log(`✨ Updating particles for field ${fieldId}`);
    }, []),

    createInteractionParticles: useCallback((interaction, sourceField, targetField) => {
      console.log(`✨ Creating interaction particles between ${sourceField.type} and ${targetField.type}`);
    }, []),

    removeFieldParticles: useCallback((fieldId) => {
      console.log(`✨ Removing particles for field ${fieldId}`);
    }, [])
  };

  // Initialize magnetic field system with integrations
  const magneticSystem = useMagneticFieldSystem({
    ...config,
    audioSystem,
    particleSystem
  });

  // Handle container clicks to create fields
  const handleContainerClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Create field with random properties
    magneticSystem.createMagneticField(x, y, {
      type: Math.random() > 0.5 ? 'attract' : 'repel',
      strength: 80 + Math.random() * 40,
      radius: 40 + Math.random() * 60
    });
  }, [magneticSystem]);

  // Handle field clicks
  const handleFieldClick = useCallback((field) => {
    console.log('Field clicked:', field);
    // Could trigger special effects or field modifications
  }, []);

  // Handle field hover
  const handleFieldHover = useCallback((field) => {
    if (field) {
      console.log('Field hovered:', field.id);
    }
  }, []);

  // Handle config changes from debug panel
  const handleConfigChange = useCallback((newConfig: Partial<typeof config>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  // Demo actions
  const createGoalCelebration = useCallback(() => {
    const centerX = width / 2;
    const centerY = height / 2;
    magneticSystem.createGoalCelebrationFields(centerX, centerY, 3);
  }, [magneticSystem, width, height]);

  const createEnergyWave = useCallback(() => {
    const path = [];
    for (let i = 0; i <= 10; i++) {
      const progress = i / 10;
      path.push({
        x: 50 + progress * (width - 100),
        y: height / 2 + Math.sin(progress * Math.PI * 2) * 100
      });
    }
    magneticSystem.createEnergySurgeFields(path);
  }, [magneticSystem, width, height]);

  // Update debug metrics periodically
  useEffect(() => {
    if (!config.enableDebugMode) return;

    const interval = setInterval(() => {
      const metrics = magneticSystem.getPerformanceMetrics();
      // Update debug metrics in the system
      // This would typically be handled internally by the hook
    }, config.debugUpdateInterval);

    return () => clearInterval(interval);
  }, [config.enableDebugMode, config.debugUpdateInterval, magneticSystem]);

  return (
    <div className={`relative bg-gray-900 rounded-lg overflow-hidden ${className}`}>
      {/* Main visualization area */}
      <div
        ref={containerRef}
        className="relative cursor-crosshair"
        style={{ width, height }}
        onClick={handleContainerClick}
      >
        <MagneticFieldRenderer
          fields={magneticSystem.visualizationData.fields}
          interactions={magneticSystem.visualizationData.interactions}
          width={width}
          height={height}
          isMobile={magneticSystem.debugMetrics?.isMobile}
          accessibilityMode={magneticSystem.accessibilityMode}
          performanceMode={magneticSystem.performanceMode}
          onFieldClick={handleFieldClick}
          onFieldHover={handleFieldHover}
          className="absolute inset-0"
        />

        {/* Overlay controls */}
        <div className="absolute top-4 left-4 space-y-2">
          <div className="bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg text-sm">
            Fields: {magneticSystem.magneticFields.length} | 
            Interactions: {magneticSystem.fieldInteractions.length}
          </div>
          
          <div className="flex flex-wrap gap-2">
            <button
              onClick={createGoalCelebration}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              🎉 Goal Celebration
            </button>
            
            <button
              onClick={createEnergyWave}
              className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              ⚡ Energy Wave
            </button>
            
            <button
              onClick={magneticSystem.clearAllFields}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              🗑️ Clear All
            </button>
            
            <button
              onClick={magneticSystem.toggleActive}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                magneticSystem.isActive 
                  ? 'bg-green-600 hover:bg-green-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              } text-white`}
            >
              {magneticSystem.isActive ? '⏸️ Pause' : '▶️ Play'}
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg text-sm max-w-xs">
          <p className="mb-1">🖱️ Click to create magnetic fields</p>
          {magneticSystem.debugMetrics?.isMobile && (
            <p>📱 Tap or swipe to create fields and energy surges</p>
          )}
        </div>

        {/* Performance indicator */}
        {magneticSystem.debugMetrics && (
          <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg text-sm">
            <div className="flex items-center space-x-2">
              <span>FPS:</span>
              <span className={
                magneticSystem.debugMetrics.framesPerSecond >= 50 ? 'text-green-400' :
                magneticSystem.debugMetrics.framesPerSecond >= 30 ? 'text-yellow-400' : 'text-red-400'
              }>
                {magneticSystem.debugMetrics.framesPerSecond}
              </span>
              {magneticSystem.debugMetrics.isMobile && (
                <span className="text-orange-400">📱</span>
              )}
              {magneticSystem.isMobileOptimized && (
                <span className="text-blue-400">⚡</span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Debug Panel */}
      {magneticSystem.debugMetrics && (
        <MagneticFieldDebugPanel
          metrics={magneticSystem.debugMetrics}
          isVisible={showDebugPanel}
          onToggleVisibility={() => setShowDebugPanel(!showDebugPanel)}
          onConfigChange={handleConfigChange}
          className="z-50"
        />
      )}
    </div>
  );
};
