import type { NewsItem, League, Fixture, Team } from '../types';

// Mock Teams
const mockTeams: Record<string, Team> = {
        manchester_united: {
                id: 'mu',
                name: 'Manchester United',
                abbreviation: 'MUN',
                shortName: 'Man United',
                logo: '/api/placeholder/40/40'
        },
        liverpool: {
                id: 'liv',
                name: 'Liverpool',
                abbreviation: 'LIV',
                shortName: 'Liverpool',
                logo: '/api/placeholder/40/40'
        },
        chelsea: {
                id: 'che',
                name: 'Chelsea',
                abbreviation: 'CHE',
                shortName: 'Chelsea',
                logo: '/api/placeholder/40/40'
        },
        arsenal: {
                id: 'ars',
                name: 'Arsenal',
                abbreviation: 'ARS',
                shortName: 'Arsenal',
                logo: '/api/placeholder/40/40'
        },
        manchester_city: {
                id: 'mci',
                name: 'Manchester City',
                abbreviation: 'MCI',
                shortName: 'Man City',
                logo: '/api/placeholder/40/40'
        },
        tottenham: {
                id: 'tot',
                name: 'Tottenham',
                abbreviation: 'TOT',
                shortName: 'Spurs',
                logo: '/api/placeholder/40/40'
        },
        barcelona: {
                id: 'bar',
                name: 'Barcelona',
                abbreviation: 'BAR',
                shortName: '<PERSON><PERSON>',
                logo: '/api/placeholder/40/40'
        },
        real_madrid: {
                id: 'rm',
                name: 'Real Madrid',
                abbreviation: 'RMA',
                shortName: 'Real Madrid',
                logo: '/api/placeholder/40/40'
        },
        bayern_munich: {
                id: 'bay',
                name: 'Bayern Munich',
                abbreviation: 'BAY',
                logo: '/api/placeholder/40/40'
        },
        psg: {
                id: 'psg',
                name: 'Paris Saint-Germain',
                abbreviation: 'PSG',
                logo: '/api/placeholder/40/40'
        }
};

// Mock News Data
export const mockNewsData: NewsItem[] = [
        {
                id: 'news-1',
                title: 'Manchester United Signs New Star Player in Record-Breaking Deal',
                summary: 'The Red Devils complete a sensational transfer that could reshape their season prospects.',
                content: 'Manchester United has officially announced the signing of...',
                publishedAt: '2025-05-30T10:30:00Z',
                author: 'Sports Reporter',
                category: 'transfer',
                imageUrl: '/api/placeholder/400/240',
                tags: ['Manchester United', 'Transfer', 'Premier League'],
                readTime: 3,
                slug: 'manchester-united-record-signing',
                isFeatured: true,
                priority: 'high'
        },
        {
                id: 'news-2',
                title: 'Champions League Quarter-Finals Draw Reveals Exciting Matchups',
                summary: 'European football elite face off in what promises to be thrilling encounters.',
                content: 'The UEFA Champions League quarter-final draw has produced...',
                publishedAt: '2025-05-30T08:15:00Z',
                author: 'UEFA Correspondent',
                category: 'league',
                imageUrl: '/api/placeholder/400/240',
                tags: ['Champions League', 'Draw', 'UEFA'],
                readTime: 4,
                slug: 'champions-league-quarter-finals-draw',
                isFeatured: true,
                priority: 'high'
        },
        {
                id: 'news-3',
                title: 'Premier League Title Race Heats Up as Season Enters Final Stretch',
                summary: 'With just weeks remaining, multiple teams still have a chance at glory.',
                content: 'The Premier League title race remains wide open...',
                publishedAt: '2025-05-30T06:45:00Z',
                author: 'League Analyst',
                category: 'league',
                imageUrl: '/api/placeholder/400/240',
                tags: ['Premier League', 'Title Race', 'Analysis'],
                readTime: 5,
                slug: 'premier-league-title-race-analysis',
                isFeatured: false,
                priority: 'medium'
        },
        {
                id: 'news-4',
                title: 'Rising Young Talent Catches Eye of Top European Clubs',
                summary: 'The 19-year-old midfielder has impressed scouts with exceptional performances.',
                content: 'A young talent from the academy has been making waves...',
                publishedAt: '2025-05-29T20:30:00Z',
                author: 'Youth Football Expert',
                category: 'transfer',
                imageUrl: '/api/placeholder/400/240',
                tags: ['Youth', 'Transfer', 'European Football'],
                readTime: 2,
                slug: 'young-talent-european-clubs-interest',
                isFeatured: false,
                priority: 'medium'
        },
        {
                id: 'news-5',
                title: 'VAR Controversy Sparks Debate After Weekend Matches',
                summary: 'Several controversial decisions have reignited discussions about technology in football.',
                content: 'The Video Assistant Referee system came under scrutiny...',
                publishedAt: '2025-05-29T18:00:00Z',
                author: 'Rules Expert',
                category: 'general',
                imageUrl: '/api/placeholder/400/240',
                tags: ['VAR', 'Controversy', 'Rules'],
                readTime: 3,
                slug: 'var-controversy-weekend-matches',
                isFeatured: false,
                priority: 'low'
        },
        {
                id: 'news-6',
                title: 'International Break Brings Injury Concerns for Club Managers',
                summary: 'Several key players picked up knocks while on international duty.',
                content: 'The latest international break has left club managers...',
                publishedAt: '2025-05-29T15:20:00Z',
                author: 'Medical Correspondent',
                category: 'general',
                imageUrl: '/api/placeholder/400/240',
                tags: ['International Break', 'Injuries', 'Clubs'],
                readTime: 4,
                slug: 'international-break-injury-concerns',
                isFeatured: false,
                priority: 'medium'
        }
];

// Mock Leagues Data
export const mockLeaguesData: League[] = [
        {
                id: 'premier-league',
                name: 'Premier League',
                logo: '/api/placeholder/60/60',
                country: 'England',
                season: '2024-25',
                description: 'The top tier of English football',
                teams: 20,
                currentMatchday: 35,
                totalMatchdays: 38,
                status: 'active',
                startDate: '2024-08-17',
                endDate: '2025-05-25'
        },
        {
                id: 'la-liga',
                name: 'La Liga',
                logo: '/api/placeholder/60/60',
                country: 'Spain',
                season: '2024-25',
                description: 'Primera División - Spanish top flight',
                teams: 20,
                currentMatchday: 36,
                totalMatchdays: 38,
                status: 'active',
                startDate: '2024-08-18',
                endDate: '2025-05-25'
        },
        {
                id: 'bundesliga',
                name: 'Bundesliga',
                logo: '/api/placeholder/60/60',
                country: 'Germany',
                season: '2024-25',
                description: 'German top division football',
                teams: 18,
                currentMatchday: 32,
                totalMatchdays: 34,
                status: 'active',
                startDate: '2024-08-23',
                endDate: '2025-05-24'
        },
        {
                id: 'serie-a',
                name: 'Serie A',
                logo: '/api/placeholder/60/60',
                country: 'Italy',
                season: '2024-25',
                description: 'Italian Serie A championship',
                teams: 20,
                currentMatchday: 36,
                totalMatchdays: 38,
                status: 'active',
                startDate: '2024-08-18',
                endDate: '2025-05-25'
        },
        {
                id: 'ligue-1',
                name: 'Ligue 1',
                logo: '/api/placeholder/60/60',
                country: 'France',
                season: '2024-25',
                description: 'French top tier football',
                teams: 18,
                currentMatchday: 33,
                totalMatchdays: 34,
                status: 'active',
                startDate: '2024-08-16',
                endDate: '2025-05-24'
        },
        {
                id: 'champions-league',
                name: 'UEFA Champions League',
                logo: '/api/placeholder/60/60',
                country: 'Europe',
                season: '2024-25',
                description: 'Premier European club competition',
                teams: 32,
                currentMatchday: 8,
                totalMatchdays: 13,
                status: 'active',
                startDate: '2024-09-17',
                endDate: '2025-06-01'
        }
];

// Mock Fixtures Data
export const mockFixturesData: Fixture[] = [
        {
                id: 'fixture-1',
                homeTeam: mockTeams.manchester_united,
                awayTeam: mockTeams.liverpool,
                scheduledTime: '2025-05-30T15:00:00Z',
                date: '2025-05-30T15:00:00Z',
                venue: 'Old Trafford',
                league: {
                        id: 'premier-league',
                        name: 'Premier League',
                        logo: '/api/placeholder/24/24'
                },
                status: 'scheduled',
                round: 'Matchday 35',
                importance: 'high'
        },
        {
                id: 'fixture-2',
                homeTeam: mockTeams.chelsea,
                awayTeam: mockTeams.arsenal,
                scheduledTime: '2025-05-30T17:30:00Z',
                date: '2025-05-30T17:30:00Z',
                venue: 'Stamford Bridge',
                league: {
                        id: 'premier-league',
                        name: 'Premier League',
                        logo: '/api/placeholder/24/24'
                },
                status: 'scheduled',
                round: 'Matchday 35',
                importance: 'high'
        },
        {
                id: 'fixture-3',
                homeTeam: mockTeams.manchester_city,
                awayTeam: mockTeams.tottenham,
                scheduledTime: '2025-05-30T12:30:00Z',
                date: '2025-05-30T12:30:00Z',
                venue: 'Etihad Stadium',
                league: {
                        id: 'premier-league',
                        name: 'Premier League',
                        logo: '/api/placeholder/24/24'
                },
                status: 'live',
                homeScore: 2,
                awayScore: 1,
                matchTime: '67\'',
                minute: 67,
                round: 'Matchday 35',
                importance: 'high'
        },
        {
                id: 'fixture-4',
                homeTeam: mockTeams.barcelona,
                awayTeam: mockTeams.real_madrid,
                scheduledTime: '2025-05-31T20:00:00Z',
                date: '2025-05-31T20:00:00Z',
                venue: 'Camp Nou',
                league: {
                        id: 'la-liga',
                        name: 'La Liga',
                        logo: '/api/placeholder/24/24'
                },
                status: 'scheduled',
                round: 'Matchday 36',
                importance: 'high'
        },
        {
                id: 'fixture-5',
                homeTeam: mockTeams.bayern_munich,
                awayTeam: mockTeams.psg,
                scheduledTime: '2025-06-01T19:45:00Z',
                date: '2025-06-01T19:45:00Z',
                venue: 'Allianz Arena',
                league: {
                        id: 'champions-league',
                        name: 'UEFA Champions League',
                        logo: '/api/placeholder/24/24'
                },
                status: 'scheduled',
                round: 'Quarter-Final',
                importance: 'high'
        },
        {
                id: 'fixture-6',
                homeTeam: mockTeams.arsenal,
                awayTeam: mockTeams.manchester_united,
                scheduledTime: '2025-05-28T20:00:00Z',
                date: '2025-05-28T20:00:00Z',
                venue: 'Emirates Stadium',
                league: {
                        id: 'premier-league',
                        name: 'Premier League',
                        logo: '/api/placeholder/24/24'
                },
                status: 'finished',
                homeScore: 3,
                awayScore: 1,
                round: 'Matchday 34',
                importance: 'medium'
        }
];
