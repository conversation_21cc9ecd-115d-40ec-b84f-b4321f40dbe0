// Advanced 3D Magnetic Field Effects System
import { useEffect, useRef, useMemo, useCallback, useReducer } from 'react';

// Mobile detection utility
const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
    window.innerWidth <= 768;
};

// Battery API detection
const getBatteryInfo = async () => {
  if ('getBattery' in navigator) {
    try {
      const battery = await (navigator as Navigator & { getBattery(): Promise<{ level: number; charging: boolean }> }).getBattery();
      return {
        level: battery.level,
        charging: battery.charging
      };
    } catch {
      return null;
    }
  }
  return null;
};

// Audio System Integration Interface
export interface AudioSystemIntegration {
  playFieldCreation?: (fieldType: 'attract' | 'repel', strength: number) => void;
  playFieldInteraction?: (interactionStrength: number, fieldTypes: string[]) => void;
  playFieldDestruction?: (fieldType: 'attract' | 'repel') => void;
  setAmbientFieldVolume?: (fieldCount: number) => void;
}

// Particle System Integration Interface
export interface ParticleSystemIntegration {
  createFieldParticles?: (field: MagneticField) => void;
  updateFieldParticles?: (fieldId: string, field: MagneticField) => void;
  createInteractionParticles?: (interaction: MagneticFieldInteraction, sourceField: MagneticField, targetField: MagneticField) => void;
  removeFieldParticles?: (fieldId: string) => void;
}

export interface MagneticField {
  id: string;
  x: number;
  y: number;
  z: number;
  radius: number;
  strength: number;
  type: 'attract' | 'repel';
  color: string;
  opacity: number;
  pulse: boolean;
  active: boolean;
  decayRate: number; // How quickly field strength diminishes over time
  createdAt: number;
  lifespan: number; // How long the field exists in ms
}

export interface MagneticFieldInteraction {
  source: string; // ID of source particle
  target: string; // ID of target particle or field
  strength: number;
  type: 'particle-field' | 'field-field';
  color: string;
  visible: boolean;
}

interface MagneticFieldSystemConfig {
  maxFields: number;
  enableVisualization: boolean;
  fieldLifespan: number;
  defaultStrength: number;
  interactionThreshold: number;
  enableFieldInteractions: boolean;
  performanceMode: boolean;
  accessibilityMode?: boolean;
  // Mobile optimization settings
  mobileOptimization?: boolean;
  autoDetectMobile?: boolean;
  batteryOptimization?: boolean;
  // System integrations
  audioSystem?: AudioSystemIntegration;
  particleSystem?: ParticleSystemIntegration;
  // Debug settings
  enableDebugMode?: boolean;
  debugUpdateInterval?: number;
}

// Debug metrics interface
export interface DebugMetrics {
  fieldCount: number;
  interactionCount: number;
  framesPerSecond: number;
  memoryUsage: number;
  batteryLevel?: number;
  isMobile: boolean;
  performanceScore: number;
  lastUpdateTime: number;
}

// Define state and actions for the reducer
interface MagneticFieldState {
  magneticFields: MagneticField[];
  fieldInteractions: MagneticFieldInteraction[];
  isActive: boolean;
  isMobileOptimized: boolean;
  debugMetrics: DebugMetrics;
}

type MagneticFieldAction =
  | { type: 'ADD_FIELD'; field: MagneticField }
  | { type: 'UPDATE_FIELDS'; updatedFields: MagneticField[] }
  | { type: 'SET_INTERACTIONS'; interactions: MagneticFieldInteraction[] }
  | { type: 'CLEAR_FIELDS' }
  | { type: 'TOGGLE_ACTIVE' }
  | { type: 'RESET_SYSTEM' }
  | { type: 'UPDATE_DEBUG_METRICS'; metrics: Partial<DebugMetrics> }
  | { type: 'SET_MOBILE_OPTIMIZATION'; enabled: boolean };

// Reducer function to handle all state changes
function magneticFieldReducer(state: MagneticFieldState, action: MagneticFieldAction): MagneticFieldState {
  switch (action.type) {
    case 'ADD_FIELD':
      return {
        ...state,
        magneticFields: [...state.magneticFields, action.field]
      };
    case 'UPDATE_FIELDS':
      return {
        ...state,
        magneticFields: action.updatedFields
      };
    case 'SET_INTERACTIONS':
      return {
        ...state,
        fieldInteractions: action.interactions
      };
    case 'CLEAR_FIELDS':
      return {
        ...state,
        magneticFields: [],
        fieldInteractions: []
      };
    case 'TOGGLE_ACTIVE':
      return {
        ...state,
        isActive: !state.isActive
      };
    case 'RESET_SYSTEM':
      return {
        ...state,
        magneticFields: [],
        fieldInteractions: [],
        isActive: true
      };
    case 'UPDATE_DEBUG_METRICS':
      return {
        ...state,
        debugMetrics: {
          ...state.debugMetrics,
          ...action.metrics
        }
      };
    case 'SET_MOBILE_OPTIMIZATION':
      return {
        ...state,
        isMobileOptimized: action.enabled
      };
    default:
      return state;
  }
}

export const useMagneticFieldSystem = (config: MagneticFieldSystemConfig) => {
  // Initialize mobile detection
  const isMobile = useMemo(() => {
    return config.autoDetectMobile !== false ? isMobileDevice() : false;
  }, [config.autoDetectMobile]);

  // Use reducer instead of multiple useState calls
  const [state, dispatch] = useReducer(magneticFieldReducer, {
    magneticFields: [],
    fieldInteractions: [],
    isActive: true,
    isMobileOptimized: config.mobileOptimization || isMobile,
    debugMetrics: {
      fieldCount: 0,
      interactionCount: 0,
      framesPerSecond: 60,
      memoryUsage: 0,
      isMobile,
      performanceScore: 100,
      lastUpdateTime: Date.now()
    }
  });

  const { magneticFields, fieldInteractions, isActive, isMobileOptimized, debugMetrics } = state;

  const animationFrameRef = useRef<number | undefined>(undefined);
  const lastUpdateRef = useRef<number>(Date.now());
  const batteryInfoRef = useRef<{ level: number; charging: boolean } | null>(null);
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);

  // Add a ref for tracking performance metrics
  const performanceMetricsRef = useRef({
    lastFrameTime: 0,
    frameCount: 0,
    avgFrameTime: 0,
    framesPerSecond: 60
  });

  // Battery monitoring effect
  useEffect(() => {
    if (!config.batteryOptimization) return;

    const updateBatteryInfo = async () => {
      const batteryInfo = await getBatteryInfo();
      if (batteryInfo) {
        batteryInfoRef.current = batteryInfo;

        // Update debug metrics with battery info
        dispatch({
          type: 'UPDATE_DEBUG_METRICS',
          metrics: {
            batteryLevel: batteryInfo.level,
            lastUpdateTime: Date.now()
          }
        });

        // Auto-enable performance mode if battery is low
        if (batteryInfo.level < 0.2 && !batteryInfo.charging) {
          dispatch({ type: 'SET_MOBILE_OPTIMIZATION', enabled: true });
        }
      }
    };

    updateBatteryInfo();
    const interval = setInterval(updateBatteryInfo, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [config.batteryOptimization]);

  // Create a new magnetic field
  const createMagneticField = useCallback((
    x: number,
    y: number,
    options: Partial<Omit<MagneticField, 'id' | 'createdAt'>> = {}
  ) => {
    const now = Date.now();
    const id = `magnetic-field-${now}-${Math.random().toString(36).substring(2, 11)}`;

    // Apply mobile optimizations
    const mobileOptimized = isMobileOptimized || config.mobileOptimization;
    const accessibilityModeActive = config.accessibilityMode || false;

    // Reduce complexity for mobile and accessibility
    const pulseEnabled = (accessibilityModeActive || mobileOptimized)
      ? false
      : (options.pulse !== undefined ? options.pulse : Math.random() > 0.3);

    // Adjust field properties for mobile
    const radiusMultiplier = mobileOptimized ? 0.7 : 1;
    const strengthMultiplier = mobileOptimized ? 0.8 : 1;

    const newField: MagneticField = {
      id,
      x,
      y,
      z: options.z || 0,
      radius: (options.radius || 50 + Math.random() * 100) * radiusMultiplier,
      strength: (options.strength || config.defaultStrength * (0.5 + Math.random())) * strengthMultiplier,
      type: options.type || (Math.random() > 0.5 ? 'attract' : 'repel'),
      color: options.color || (options.type === 'attract' ? '#3b82f6' : '#ef4444'),
      opacity: options.opacity || 0.3 + Math.random() * 0.3,
      pulse: pulseEnabled,
      active: options.active !== undefined ? options.active : true,
      decayRate: options.decayRate || 0.01 + Math.random() * 0.03,
      createdAt: now,
      lifespan: options.lifespan || config.fieldLifespan * (0.7 + Math.random() * 0.6)
    };

    dispatch({ type: 'ADD_FIELD', field: newField });

    // Enforce max fields limit (reduced for mobile)
    const maxFieldsLimit = mobileOptimized ? Math.floor(config.maxFields * 0.6) : config.maxFields;
    if (magneticFields.length >= maxFieldsLimit) {
      const updatedFields = [...magneticFields, newField].slice(1); // Remove oldest field
      dispatch({ type: 'UPDATE_FIELDS', updatedFields });
    }

    // Integrate with audio system
    if (config.audioSystem?.playFieldCreation) {
      config.audioSystem.playFieldCreation(newField.type, newField.strength);
    }

    // Integrate with particle system
    if (config.particleSystem?.createFieldParticles) {
      config.particleSystem.createFieldParticles(newField);
    }

    return id;
  }, [config, magneticFields, isMobileOptimized]);

  // Create a goal celebration field burst
  const createGoalCelebrationFields = useCallback((x: number, y: number, intensity: number = 1) => {
    const fieldCount = Math.floor(4 + intensity * 4);
    const fieldIds: string[] = [];

    // Create central explosion field
    fieldIds.push(createMagneticField(x, y, {
      radius: 150 * intensity,
      strength: config.defaultStrength * 3 * intensity,
      type: 'repel',
      color: '#10b981', // Green
      opacity: 0.6,
      pulse: true,
      lifespan: config.fieldLifespan * 0.7
    }));

    // Create surrounding fields
    for (let i = 0; i < fieldCount; i++) {
      const angle = (Math.PI * 2) * (i / fieldCount);
      const distance = 100 + Math.random() * 150;

      fieldIds.push(createMagneticField(
        x + Math.cos(angle) * distance,
        y + Math.sin(angle) * distance,
        {
          radius: 50 + Math.random() * 70,
          strength: config.defaultStrength * (1 + Math.random()),
          type: i % 2 === 0 ? 'attract' : 'repel',
          color: i % 3 === 0 ? '#f59e0b' : i % 3 === 1 ? '#3b82f6' : '#8b5cf6',
          opacity: 0.3 + Math.random() * 0.4,
          pulse: true,
          lifespan: config.fieldLifespan * (0.5 + Math.random() * 0.5)
        }
      ));
    }

    return fieldIds;
  }, [createMagneticField, config.defaultStrength, config.fieldLifespan]);

  // Create energy surge fields along a path
  const createEnergySurgeFields = useCallback((
    path: { x: number; y: number }[],
    options: Partial<Omit<MagneticField, 'id' | 'createdAt' | 'x' | 'y'>> = {}
  ) => {
    const fieldIds: string[] = [];

    // Reduce field count for performance mode
    const effectivePath = config.performanceMode ?
      path.filter((_, i) => i % 2 === 0) : // Take every other point in performance mode
      path;

    // Create magnetic fields along the path with staggered timing
    effectivePath.forEach((point, index) => {
      setTimeout(() => {
        const fieldId = createMagneticField(point.x, point.y, {
          radius: options.radius || 30 + Math.random() * 20,
          strength: options.strength || 50 + Math.random() * 30,
          type: options.type || 'attract',
          color: options.color || '#3b82f6',
          opacity: options.opacity || 0.4 + Math.random() * 0.3,
          pulse: options.pulse !== undefined ? options.pulse : true,
          decayRate: options.decayRate || 0.03 + Math.random() * 0.02,
          lifespan: options.lifespan || config.fieldLifespan * 0.6,
          z: options.z || index * 2
        });

        fieldIds.push(fieldId);
      }, index * (config.performanceMode ? 150 : 100)); // Increase time between creation for performance mode
    });

    return fieldIds;
  }, [createMagneticField, config.fieldLifespan, config.performanceMode]);

  // Helper function to add valid interactions
  const addInteractionIfValid = useCallback((field1: MagneticField, field2: MagneticField, interactions: MagneticFieldInteraction[]) => {
    // Calculate distance between fields
    const dx = field2.x - field1.x;
    const dy = field2.y - field1.y;
    const dz = field2.z - field1.z;
    const distanceSquared = dx * dx + dy * dy + dz * dz;

    // Early optimization: Use square of distance to avoid sqrt operation
    const interactionRange = field1.radius + field2.radius;
    const interactionRangeSquared = interactionRange * interactionRange;

    if (distanceSquared < interactionRangeSquared) {
      const distance = Math.sqrt(distanceSquared); // Only calculate sqrt when needed
      const strengthFactor = Math.min(1, (interactionRange - distance) / interactionRange);
      const interactionStrength = field1.strength * field2.strength * strengthFactor;

      // Only show significant interactions
      if (interactionStrength > config.interactionThreshold) {
        // Determine interaction color based on field types
        let color = '#ffffff';
        if (field1.type === 'attract' && field2.type === 'attract') {
          color = '#3b82f6'; // Blue
        } else if (field1.type === 'repel' && field2.type === 'repel') {
          color = '#ef4444'; // Red
        } else {
          color = '#8b5cf6'; // Purple for mixed types
        }

        interactions.push({
          source: field1.id,
          target: field2.id,
          strength: interactionStrength,
          type: 'field-field',
          color,
          visible: config.enableVisualization
        });
      }
    }
  }, [config.interactionThreshold, config.enableVisualization]);

  // Calculate field interactions with improved performance
  const calculateFieldInteractions = useCallback(() => {
    if (!config.enableFieldInteractions || magneticFields.length <= 1) {
      dispatch({ type: 'SET_INTERACTIONS', interactions: [] });
      return;
    }

    const interactions: MagneticFieldInteraction[] = [];

    // Use spatial partitioning for larger field counts
    if (magneticFields.length > 20 && config.performanceMode) {
      // Simple grid-based approach for demonstration
      const grid: Record<string, MagneticField[]> = {};
      const cellSize = 200; // Size of each grid cell

      // Assign fields to grid cells
      magneticFields.forEach(field => {
        const cellX = Math.floor(field.x / cellSize);
        const cellY = Math.floor(field.y / cellSize);
        const cellKey = `${cellX},${cellY}`;

        if (!grid[cellKey]) {
          grid[cellKey] = [];
        }
        grid[cellKey].push(field);
      });

      // Only check interactions between fields in the same or adjacent cells
      Object.keys(grid).forEach(cellKey => {
        const [cellX, cellY] = cellKey.split(',').map(Number);

        // Check interactions within current cell
        const currentCellFields = grid[cellKey];
        for (let i = 0; i < currentCellFields.length; i++) {
          for (let j = i + 1; j < currentCellFields.length; j++) {
            addInteractionIfValid(currentCellFields[i], currentCellFields[j], interactions);
          }
        }

        // Check interactions with adjacent cells
        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            if (dx === 0 && dy === 0) continue; // Skip current cell

            const neighborKey = `${cellX + dx},${cellY + dy}`;
            const neighborFields = grid[neighborKey];

            if (neighborFields) {
              for (let i = 0; i < currentCellFields.length; i++) {
                for (let j = 0; j < neighborFields.length; j++) {
                  addInteractionIfValid(currentCellFields[i], neighborFields[j], interactions);
                }
              }
            }
          }
        }
      });
    } else {
      // Original algorithm for smaller field counts
      for (let i = 0; i < magneticFields.length; i++) {
        for (let j = i + 1; j < magneticFields.length; j++) {
          addInteractionIfValid(magneticFields[i], magneticFields[j], interactions);
        }
      }
    }

    dispatch({ type: 'SET_INTERACTIONS', interactions });
  }, [config.enableFieldInteractions, config.performanceMode, magneticFields, addInteractionIfValid]);

  // Update magnetic fields (fade out, remove expired) with performance optimization
  useEffect(() => {
    if (!isActive) return;

    const updateFields = (timestamp: number) => {
      // Calculate delta time using timestamp from requestAnimationFrame
      const deltaTime = lastUpdateRef.current ? timestamp - lastUpdateRef.current : 16.67;
      lastUpdateRef.current = timestamp;

      // Track performance metrics
      if (config.performanceMode) {
        performanceMetricsRef.current.frameCount++;
        const elapsed = timestamp - performanceMetricsRef.current.lastFrameTime;

        if (elapsed > 1000) { // Update every second
          performanceMetricsRef.current.framesPerSecond = Math.round(
            (performanceMetricsRef.current.frameCount * 1000) / elapsed
          );
          performanceMetricsRef.current.frameCount = 0;
          performanceMetricsRef.current.lastFrameTime = timestamp;

          // Adjust updates based on performance
          if (performanceMetricsRef.current.framesPerSecond < 30 && magneticFields.length > 10) {
            // Reduce update frequency for low FPS
            setTimeout(() => {
              animationFrameRef.current = requestAnimationFrame(updateFields);
            }, 32); // Skip some frames
            return;
          }
        }
      }

      // Use batch update pattern to reduce React renders
      const now = Date.now();

      const updatedFields = magneticFields
        .map(field => {
          // Calculate age and remaining lifespan
          const age = now - field.createdAt;
          const remainingLifespan = field.lifespan - age;

          if (remainingLifespan <= 0) {
            return null; // Field expired
          }

          // Calculate decay based on age
          const lifeProgress = age / field.lifespan;
          const decayFactor = 1 - lifeProgress;

          return {
            ...field,
            strength: field.strength * (1 - field.decayRate * (deltaTime / 16)),
            opacity: field.opacity * decayFactor
          };
        })
        .filter(Boolean) as MagneticField[];

      dispatch({ type: 'UPDATE_FIELDS', updatedFields });

      // Reduce calculation frequency in performance mode
      if (config.performanceMode) {
        if (performanceMetricsRef.current.frameCount % 2 === 0) {
          calculateFieldInteractions();
        }
      } else {
        calculateFieldInteractions();
      }

      // Continue animation loop
      animationFrameRef.current = requestAnimationFrame(updateFields);
    };

    // Start animation loop
    animationFrameRef.current = requestAnimationFrame(updateFields);

    // Cleanup
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isActive, calculateFieldInteractions, magneticFields, config.performanceMode]);

  // Clear all fields
  const clearAllFields = useCallback(() => {
    dispatch({ type: 'CLEAR_FIELDS' });
  }, []);

  // Reset the system
  const resetSystem = useCallback(() => {
    dispatch({ type: 'RESET_SYSTEM' });
  }, []);

  // Pause/resume system
  const toggleActive = useCallback(() => {
    dispatch({ type: 'TOGGLE_ACTIVE' });
  }, []);

  // Generate visualization data for rendering with accessibility enhancements
  const visualizationData = useMemo(() => {
    if (!config.enableVisualization) return { fields: [], interactions: [] };

    // Process fields for visualization with accessibility considerations
    const fieldData = magneticFields.map(field => {
      // Reduce or eliminate pulsing for accessibility mode
      const pulseAmplitude = field.pulse && !config.accessibilityMode
        ? Math.sin(Date.now() * 0.003) * 0.2 + 0.8
        : 1;

      // Use high contrast colors for accessibility mode
      const fieldColor = config.accessibilityMode
        ? field.type === 'attract' ? '#0040FF' : '#FF0000' // Higher contrast blue/red
        : field.color;

      return {
        ...field,
        visualRadius: field.radius * pulseAmplitude,
        visualOpacity: field.opacity * pulseAmplitude,
        gradient: field.type === 'attract'
          ? `radial-gradient(circle, ${fieldColor}80 0%, ${fieldColor}00 100%)`
          : `radial-gradient(circle, ${fieldColor}80 30%, ${fieldColor}00 100%)`,
        // Add aria attributes for screen readers
        ariaLabel: `${field.type} magnetic field with strength ${Math.round(field.strength)}`
      };
    });

    // Process interactions for visualization, with simplification for accessibility mode
    const interactionData = config.accessibilityMode && magneticFields.length > 10
      ? [] // Skip rendering interactions in accessibility mode with many fields
      : fieldInteractions.map(interaction => {
        const sourceField = magneticFields.find(f => f.id === interaction.source);
        const targetField = magneticFields.find(f => f.id === interaction.target);

        if (!sourceField || !targetField) return null;

        const dx = targetField.x - sourceField.x;
        const dy = targetField.y - sourceField.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const angle = Math.atan2(dy, dx) * (180 / Math.PI);

        // Thicker, more visible lines for accessibility mode
        const lineThickness = config.accessibilityMode ? 3 : 1;

        return {
          ...interaction,
          x1: sourceField.x,
          y1: sourceField.y,
          x2: targetField.x,
          y2: targetField.y,
          length: distance,
          angle,
          opacity: interaction.strength * 0.2,
          thickness: lineThickness,
          // Add aria label for interactions
          ariaLabel: `Interaction between ${sourceField.type} and ${targetField.type} fields`
        };
      }).filter(Boolean);

    return {
      fields: fieldData,
      interactions: interactionData as Array<{
        x1: number;
        y1: number;
        x2: number;
        y2: number;
        length: number;
        angle: number;
        opacity: number;
        thickness: number;
        source: string;
        target: string;
        strength: number;
        type: 'particle-field' | 'field-field';
        color: string;
        visible: boolean;
        ariaLabel?: string;
      }>
    };
  }, [config.enableVisualization, magneticFields, fieldInteractions, config.accessibilityMode]);

  // New method to get performance metrics with enhanced debug info
  const getPerformanceMetrics = useCallback(() => {
    const batteryInfo = batteryInfoRef.current;
    return {
      ...debugMetrics,
      fieldCount: magneticFields.length,
      interactionCount: fieldInteractions.length,
      framesPerSecond: performanceMetricsRef.current.framesPerSecond,
      memoryUsage: magneticFields.length * 200 + fieldInteractions.length * 100,
      batteryLevel: batteryInfo?.level,
      isMobile,
      performanceScore: Math.min(100, Math.max(0,
        (performanceMetricsRef.current.framesPerSecond / 60) * 100 -
        (magneticFields.length > 20 ? 20 : 0) -
        (batteryInfo && batteryInfo.level < 0.3 ? 30 : 0)
      )),
      lastUpdateTime: Date.now()
    };
  }, [magneticFields.length, fieldInteractions.length, debugMetrics, isMobile]);

  // Mobile gesture support
  const handleTouchStart = useCallback((event: TouchEvent) => {
    if (!isMobile || event.touches.length !== 1) return;

    const touch = event.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
  }, [isMobile]);

  const handleTouchEnd = useCallback((event: TouchEvent) => {
    if (!isMobile || !touchStartRef.current) return;

    const touch = event.changedTouches[0];
    const touchStart = touchStartRef.current;
    const deltaTime = Date.now() - touchStart.time;
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Detect tap gesture (short duration, small movement)
    if (deltaTime < 300 && distance < 10) {
      // Create field at touch position
      createMagneticField(touch.clientX, touch.clientY, {
        type: Math.random() > 0.5 ? 'attract' : 'repel',
        strength: config.defaultStrength * 0.8 // Reduced for mobile
      });
    }
    // Detect swipe gesture (longer distance)
    else if (distance > 50 && deltaTime < 500) {
      // Create energy surge along swipe path
      const steps = Math.floor(distance / 30);
      const path = [];
      for (let i = 0; i <= steps; i++) {
        const progress = i / steps;
        path.push({
          x: touchStart.x + deltaX * progress,
          y: touchStart.y + deltaY * progress
        });
      }
      createEnergySurgeFields(path, { strength: config.defaultStrength * 0.6 });
    }

    touchStartRef.current = null;
  }, [isMobile, createMagneticField, createEnergySurgeFields, config.defaultStrength]);

  // Add touch event listeners for mobile
  useEffect(() => {
    if (!isMobile) return;

    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isMobile, handleTouchStart, handleTouchEnd]);

  return {
    magneticFields,
    fieldInteractions,
    createMagneticField,
    createGoalCelebrationFields,
    createEnergySurgeFields,
    clearAllFields,
    resetSystem,
    toggleActive,
    isActive,
    isMobileOptimized,
    debugMetrics,
    visualizationData,
    getPerformanceMetrics,
    performanceMode: config.performanceMode,
    accessibilityMode: config.accessibilityMode,
    // New mobile-specific methods
    handleTouchStart,
    handleTouchEnd
  };
};
